const PQueue = require("p-queue").default;
const xTransferQueue = new PQueue({ concurrency: 1 });
const axios = require("axios");
const { saveBase64Image } = require("../utils/base64ToImage");
const { omit } = require("lodash");

const test = async ({ country, phNumber, proxyCountry }) => {
  console.table({ country, phNumber, proxyCountry });
  const options = {
    method: "POST",
    url: "https://production-sfo.browserless.io/chrome/bql",
    params: {
      token: "69f3c87c-5c9a-49d0-8589-a27f1fa9bffe",
      proxy: "residential",
      proxyCountry,
      humanlike: true,
      adBlock: true,
      blockConsentModals: true,
    },
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      query: `
mutation XTransferScraper {
  # Set proxy (sticky IP)
  useProxy: proxy(
    country: ${proxyCountry.toUpperCase()}
    sticky: true
    type: [document, xhr]
  ) {
    time
  }

  # Block images and media for stealth and speed
  blockResources: reject(
    type: [image, media]
  ) {
    enabled
  }

  # Navigate to registration page
  openPage: goto(
    url: "https://www.xtransfer.com/base/login/register?lang=en"
    waitUntil: firstMeaningfulPaint
  ) {
    status
  }

  # Open area code dropdown
  openDropdown: click(
    selector: ".atom-dropdown-trigger.area-code-selector"
  ) {
    time
  }

  # Type country (no delay)
  typeCountry: type(
    selector: "input[placeholder='Search for a country or area code']"
    text: "${country}"
     delay: [40, 120]
  ) {
    time
  }

  # Select matching country code from dropdown
  selectCode: evaluate(content: """
    (() => {
      const containers = document.querySelectorAll('.sc-dropdown-container-code .sc-dropdown-container-item');
      for (const container of containers) {
        const codeSpan = container.querySelector('span:last-child');
        if (codeSpan && codeSpan.textContent.trim() === "${country}") {
          container.click();
          return;
        }
      }
    })()
  """) {
    time
  }

  # Type phone number (no delay)
  typePhone: type(
    selector: "input[name='phone']"
    text: "${phNumber}"
    delay: []
  ) {
    time
  }

  # Focus phone code input
  focusCodeInput: click(
    selector: "input[id='phone-code-input']"
  ) {
    time
  }

  # Wait for notice to appear
  waitForMessage: waitForSelector(
    selector: ".atom-message .atom-message-notice .atom-message-notice-content"
  ) {
    time
  }

  # Wait 3 seconds for confirmation message
  wait3Sec: waitForTimeout(time: 3000) {
    time
  }

  # Take full page screenshot
  screenshot(fullPage: true) {
    base64
  }
}

    `,
      operationName: "XTransferScraper",
    },
  };

  const { data } = await axios.request(options);
  // if image exists save it
  if (data?.data?.screenshot?.base64) {
    saveBase64Image({
      base64Data: data.data.screenshot.base64,
      filename: `${country}-${phNumber}-${proxyCountry}`,
    });
  }

  const omittedBase64 = omit(data, "data.screenshot.base64");
  console.table(omittedBase64);
};

const runXtranferTest = ({ country, phNumber, proxyCountry }) =>
  xTransferQueue.add(() => test({ country, phNumber, proxyCountry }));

module.exports = {
  runXtranferTest,
};
