#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules/msgpackr-extract/bin/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules/msgpackr-extract/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules/msgpackr-extract/bin/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules/msgpackr-extract/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/msgpackr-extract@3.0.3/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../msgpackr-extract/bin/download-prebuilds.js" "$@"
else
  exec node  "$basedir/../msgpackr-extract/bin/download-prebuilds.js" "$@"
fi
