#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/tester/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/tester/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/home/<USER>/Desktop/tester/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../optional.js" "$@"
else
  exec node  "$basedir/../../optional.js" "$@"
fi
