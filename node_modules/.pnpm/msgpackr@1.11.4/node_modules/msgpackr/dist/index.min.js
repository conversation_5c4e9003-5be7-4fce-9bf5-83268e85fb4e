!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).msgpackr={})}(this,(function(e){"use strict";var t,r,n;try{t=new TextDecoder}catch(e){}var i,s,o,u,a,l=0,f={},c=0,h=0,g=[],p={useRecords:!1,mapsAsObjects:!0};class d{}const y=new d;y.name="MessagePack 0xC1";var w=!1,b=2;try{new Function("")}catch(e){b=1/0}class m{constructor(e){e&&(!1===e.useRecords&&void 0===e.mapsAsObjects&&(e.mapsAsObjects=!0),e.sequential&&!1!==e.trusted&&(e.trusted=!0,e.structures||0==e.useRecords||(e.structures=[],e.maxSharedStructures||(e.maxSharedStructures=0))),e.structures?e.structures.sharedLength=e.structures.length:e.getStructures&&((e.structures=[]).uninitialized=!0,e.structures.sharedLength=0),e.int64AsNumber&&(e.int64AsType="number")),Object.assign(this,e)}unpack(e,t){if(r)return q((()=>(Z(),this?this.unpack(e,t):m.prototype.unpack.call(p,e,t))));e.buffer||e.constructor!==ArrayBuffer||(e="undefined"!=typeof Buffer?Buffer.from(e):new Uint8Array(e)),"object"==typeof t?(n=t.end||e.length,l=t.start||0):(l=0,n=t>-1?t:e.length),h=0,s=null,o=null,r=e;try{a=e.dataView||(e.dataView=new DataView(e.buffer,e.byteOffset,e.byteLength))}catch(t){if(r=null,e instanceof Uint8Array)throw t;throw new Error("Source must be a Uint8Array or Buffer but was a "+(e&&"object"==typeof e?e.constructor.name:typeof e))}if(this instanceof m){if(f=this,this.structures)return i=this.structures,S(t);(!i||i.length>0)&&(i=[])}else f=p,(!i||i.length>0)&&(i=[]);return S(t)}unpackMultiple(e,t){let r,n=0;try{w=!0;let i=e.length,s=this?this.unpack(e,i):Q.unpack(e,i);if(!t){for(r=[s];l<i;)n=l,r.push(S());return r}if(!1===t(s,n,l))return;for(;l<i;)if(n=l,!1===t(S(),n,l))return}catch(e){throw e.lastPosition=n,e.values=r,e}finally{w=!1,Z()}}_mergeStructures(e,t){e=e||[],Object.isFrozen(e)&&(e=e.map((e=>e.slice(0))));for(let t=0,r=e.length;t<r;t++){let r=e[t];r&&(r.isShared=!0,t>=32&&(r.highByte=t-32>>5))}e.sharedLength=e.length;for(let r in t||[])if(r>=0){let n=e[r],i=t[r];i&&(n&&((e.restoreStructures||(e.restoreStructures=[]))[r]=n),e[r]=i)}return this.structures=e}decode(e,t){return this.unpack(e,t)}}function S(e){try{if(!f.trusted&&!w){let e=i.sharedLength||0;e<i.length&&(i.length=e)}let e;if(f.randomAccessStructure&&r[l]<64&&r[l],e=A(),o&&(l=o.postBundlePosition,o=null),w&&(i.restoreStructures=null),l==n)i&&i.restoreStructures&&I(),i=null,r=null,u&&(u=null);else{if(l>n)throw new Error("Unexpected end of MessagePack data");if(!w){let t;try{t=JSON.stringify(e,((e,t)=>"bigint"==typeof t?`${t}n`:t)).slice(0,100)}catch(e){t="(JSON view not available "+e+")"}throw new Error("Data read, but end of buffer not reached "+t)}}return e}catch(e){throw i&&i.restoreStructures&&I(),Z(),(e instanceof RangeError||e.message.startsWith("Unexpected end of buffer")||l>n)&&(e.incomplete=!0),e}}function I(){for(let e in i.restoreStructures)i[e]=i.restoreStructures[e];i.restoreStructures=null}function A(){let e=r[l++];if(e<160){if(e<128){if(e<64)return e;{let t=i[63&e]||f.getStructures&&v()[63&e];return t?(t.read||(t.read=E(t,63&e)),t.read()):e}}if(e<144){if(e-=128,f.mapsAsObjects){let t={};for(let r=0;r<e;r++){let e=z();"__proto__"===e&&(e="__proto_"),t[e]=A()}return t}{let t=new Map;for(let r=0;r<e;r++)t.set(A(),A());return t}}{e-=144;let t=new Array(e);for(let r=0;r<e;r++)t[r]=A();return f.freezeData?Object.freeze(t):t}}if(e<192){let t=e-160;if(h>=l)return s.slice(l-c,(l+=t)-c);if(0==h&&n<140){let e=t<16?R(t):V(t);if(null!=e)return e}return B(t)}{let t;switch(e){case 192:return null;case 193:return o?(t=A(),t>0?o[1].slice(o.position1,o.position1+=t):o[0].slice(o.position0,o.position0-=t)):y;case 194:return!1;case 195:return!0;case 196:if(t=r[l++],void 0===t)throw new Error("Unexpected end of buffer");return L(t);case 197:return t=a.getUint16(l),l+=2,L(t);case 198:return t=a.getUint32(l),l+=4,L(t);case 199:return N(r[l++]);case 200:return t=a.getUint16(l),l+=2,N(t);case 201:return t=a.getUint32(l),l+=4,N(t);case 202:if(t=a.getFloat32(l),f.useFloat32>2){let e=G[(127&r[l])<<1|r[l+1]>>7];return l+=4,(e*t+(t>0?.5:-.5)>>0)/e}return l+=4,t;case 203:return t=a.getFloat64(l),l+=8,t;case 204:return r[l++];case 205:return t=a.getUint16(l),l+=2,t;case 206:return t=a.getUint32(l),l+=4,t;case 207:return"number"===f.int64AsType?(t=4294967296*a.getUint32(l),t+=a.getUint32(l+4)):"string"===f.int64AsType?t=a.getBigUint64(l).toString():"auto"===f.int64AsType?(t=a.getBigUint64(l),t<=BigInt(2)<<BigInt(52)&&(t=Number(t))):t=a.getBigUint64(l),l+=8,t;case 208:return a.getInt8(l++);case 209:return t=a.getInt16(l),l+=2,t;case 210:return t=a.getInt32(l),l+=4,t;case 211:return"number"===f.int64AsType?(t=4294967296*a.getInt32(l),t+=a.getUint32(l+4)):"string"===f.int64AsType?t=a.getBigInt64(l).toString():"auto"===f.int64AsType?(t=a.getBigInt64(l),t>=BigInt(-2)<<BigInt(52)&&t<=BigInt(2)<<BigInt(52)&&(t=Number(t))):t=a.getBigInt64(l),l+=8,t;case 212:if(t=r[l++],114==t)return W(63&r[l++]);{let e=g[t];if(e)return e.read?(l++,e.read(A())):e.noBuffer?(l++,e()):e(r.subarray(l,++l));throw new Error("Unknown extension "+t)}case 213:return t=r[l],114==t?(l++,W(63&r[l++],r[l++])):N(2);case 214:return N(4);case 215:return N(8);case 216:return N(16);case 217:return t=r[l++],h>=l?s.slice(l-c,(l+=t)-c):O(t);case 218:return t=a.getUint16(l),h>=(l+=2)?s.slice(l-c,(l+=t)-c):_(t);case 219:return t=a.getUint32(l),h>=(l+=4)?s.slice(l-c,(l+=t)-c):x(t);case 220:return t=a.getUint16(l),l+=2,M(t);case 221:return t=a.getUint32(l),l+=4,M(t);case 222:return t=a.getUint16(l),l+=2,j(t);case 223:return t=a.getUint32(l),l+=4,j(t);default:if(e>=224)return e-256;if(void 0===e){let e=new Error("Unexpected end of MessagePack data");throw e.incomplete=!0,e}throw new Error("Unknown MessagePack token "+e)}}}const U=/^[a-zA-Z_$][a-zA-Z\d_$]*$/;function E(e,t){function r(){if(r.count++>b){let r=e.read=new Function("r","return function(){return "+(f.freezeData?"Object.freeze":"")+"({"+e.map((e=>"__proto__"===e?"__proto_:r()":U.test(e)?e+":r()":"["+JSON.stringify(e)+"]:r()")).join(",")+"})}")(A);return 0===e.highByte&&(e.read=k(t,e.read)),r()}let n={};for(let t=0,r=e.length;t<r;t++){let r=e[t];"__proto__"===r&&(r="__proto_"),n[r]=A()}return f.freezeData?Object.freeze(n):n}return r.count=0,0===e.highByte?k(t,r):r}const k=(e,t)=>function(){let n=r[l++];if(0===n)return t();let s=e<32?-(e+(n<<5)):e+(n<<5),o=i[s]||v()[s];if(!o)throw new Error("Record id is not defined for "+s);return o.read||(o.read=E(o,e)),o.read()};function v(){let e=q((()=>(r=null,f.getStructures())));return i=f._mergeStructures(e,i)}var B=T,O=T,_=T,x=T;function T(e){let n;if(e<16&&(n=R(e)))return n;if(e>64&&t)return t.decode(r.subarray(l,l+=e));const i=l+e,s=[];for(n="";l<i;){const e=r[l++];if(0==(128&e))s.push(e);else if(192==(224&e)){const t=63&r[l++];s.push((31&e)<<6|t)}else if(224==(240&e)){const t=63&r[l++],n=63&r[l++];s.push((31&e)<<12|t<<6|n)}else if(240==(248&e)){let t=(7&e)<<18|(63&r[l++])<<12|(63&r[l++])<<6|63&r[l++];t>65535&&(t-=65536,s.push(t>>>10&1023|55296),t=56320|1023&t),s.push(t)}else s.push(e);s.length>=4096&&(n+=D.apply(String,s),s.length=0)}return s.length>0&&(n+=D.apply(String,s)),n}function M(e){let t=new Array(e);for(let r=0;r<e;r++)t[r]=A();return f.freezeData?Object.freeze(t):t}function j(e){if(f.mapsAsObjects){let t={};for(let r=0;r<e;r++){let e=z();"__proto__"===e&&(e="__proto_"),t[e]=A()}return t}{let t=new Map;for(let r=0;r<e;r++)t.set(A(),A());return t}}var D=String.fromCharCode;function V(e){let t=l,n=new Array(e);for(let i=0;i<e;i++){const e=r[l++];if((128&e)>0)return void(l=t);n[i]=e}return D.apply(String,n)}function R(e){if(e<4){if(e<2){if(0===e)return"";{let e=r[l++];return(128&e)>1?void(l-=1):D(e)}}{let t=r[l++],n=r[l++];if((128&t)>0||(128&n)>0)return void(l-=2);if(e<3)return D(t,n);let i=r[l++];return(128&i)>0?void(l-=3):D(t,n,i)}}{let t=r[l++],n=r[l++],i=r[l++],s=r[l++];if((128&t)>0||(128&n)>0||(128&i)>0||(128&s)>0)return void(l-=4);if(e<6){if(4===e)return D(t,n,i,s);{let e=r[l++];return(128&e)>0?void(l-=5):D(t,n,i,s,e)}}if(e<8){let o=r[l++],u=r[l++];if((128&o)>0||(128&u)>0)return void(l-=6);if(e<7)return D(t,n,i,s,o,u);let a=r[l++];return(128&a)>0?void(l-=7):D(t,n,i,s,o,u,a)}{let o=r[l++],u=r[l++],a=r[l++],f=r[l++];if((128&o)>0||(128&u)>0||(128&a)>0||(128&f)>0)return void(l-=8);if(e<10){if(8===e)return D(t,n,i,s,o,u,a,f);{let e=r[l++];return(128&e)>0?void(l-=9):D(t,n,i,s,o,u,a,f,e)}}if(e<12){let c=r[l++],h=r[l++];if((128&c)>0||(128&h)>0)return void(l-=10);if(e<11)return D(t,n,i,s,o,u,a,f,c,h);let g=r[l++];return(128&g)>0?void(l-=11):D(t,n,i,s,o,u,a,f,c,h,g)}{let c=r[l++],h=r[l++],g=r[l++],p=r[l++];if((128&c)>0||(128&h)>0||(128&g)>0||(128&p)>0)return void(l-=12);if(e<14){if(12===e)return D(t,n,i,s,o,u,a,f,c,h,g,p);{let e=r[l++];return(128&e)>0?void(l-=13):D(t,n,i,s,o,u,a,f,c,h,g,p,e)}}{let d=r[l++],y=r[l++];if((128&d)>0||(128&y)>0)return void(l-=14);if(e<15)return D(t,n,i,s,o,u,a,f,c,h,g,p,d,y);let w=r[l++];return(128&w)>0?void(l-=15):D(t,n,i,s,o,u,a,f,c,h,g,p,d,y,w)}}}}}function F(){let e,t=r[l++];if(t<192)e=t-160;else switch(t){case 217:e=r[l++];break;case 218:e=a.getUint16(l),l+=2;break;case 219:e=a.getUint32(l),l+=4;break;default:throw new Error("Expected string")}return T(e)}function L(e){return f.copyBuffers?Uint8Array.prototype.slice.call(r,l,l+=e):r.subarray(l,l+=e)}function N(e){let t=r[l++];if(g[t]){let n;return g[t](r.subarray(l,n=l+=e),(e=>{l=e;try{return A()}finally{l=n}}))}throw new Error("Unknown extension type "+t)}var P=new Array(4096);function z(){let e=r[l++];if(!(e>=160&&e<192))return l--,C(A());if(e-=160,h>=l)return s.slice(l-c,(l+=e)-c);if(!(0==h&&n<180))return B(e);let t,i=4095&(e<<5^(e>1?a.getUint16(l):e>0?r[l]:0)),o=P[i],u=l,f=l+e-3,g=0;if(o&&o.bytes==e){for(;u<f;){if(t=a.getUint32(u),t!=o[g++]){u=1879048192;break}u+=4}for(f+=3;u<f;)if(t=r[u++],t!=o[g++]){u=1879048192;break}if(u===f)return l=u,o.string;f-=3,u=l}for(o=[],P[i]=o,o.bytes=e;u<f;)t=a.getUint32(u),o.push(t),u+=4;for(f+=3;u<f;)t=r[u++],o.push(t);let p=e<16?R(e):V(e);return o.string=null!=p?p:B(e)}function C(e){if("string"==typeof e)return e;if("number"==typeof e||"boolean"==typeof e||"bigint"==typeof e)return e.toString();if(null==e)return e+"";if(f.allowArraysInMapKeys&&Array.isArray(e)&&e.flat().every((e=>["string","number","boolean","bigint"].includes(typeof e))))return e.flat().toString();throw new Error("Invalid property type for record: "+typeof e)}const W=(e,t)=>{let r=A().map(C),n=e;void 0!==t&&(e=e<32?-((t<<5)+e):(t<<5)+e,r.highByte=t);let s=i[e];return s&&(s.isShared||w)&&((i.restoreStructures||(i.restoreStructures=[]))[e]=s),i[e]=r,r.read=E(r,n),r.read()};g[0]=()=>{},g[0].noBuffer=!0,g[66]=e=>{let t=e.length,r=BigInt(128&e[0]?e[0]-256:e[0]);for(let n=1;n<t;n++)r<<=BigInt(8),r+=BigInt(e[n]);return r};let J={Error:Error,TypeError:TypeError,ReferenceError:ReferenceError};g[101]=()=>{let e=A();return(J[e[0]]||Error)(e[1],{cause:e[2]})},g[105]=e=>{if(!1===f.structuredClone)throw new Error("Structured clone extension is disabled");let t=a.getUint32(l-4);u||(u=new Map);let n,i=r[l];n=i>=144&&i<160||220==i||221==i?[]:i>=128&&i<144||222==i||223==i?new Map:(i>=199&&i<=201||i>=212&&i<=216)&&115===r[l+1]?new Set:{};let s={target:n};u.set(t,s);let o=A();if(!s.used)return s.target=o;if(Object.assign(n,o),n instanceof Map)for(let[e,t]of o.entries())n.set(e,t);if(n instanceof Set)for(let e of Array.from(o))n.add(e);return n},g[112]=e=>{if(!1===f.structuredClone)throw new Error("Structured clone extension is disabled");let t=a.getUint32(l-4),r=u.get(t);return r.used=!0,r.target},g[115]=()=>new Set(A());const $=["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64","BigInt64","BigUint64"].map((e=>e+"Array"));let K="object"==typeof globalThis?globalThis:window;g[116]=e=>{let t=e[0],r=Uint8Array.prototype.slice.call(e,1).buffer,n=$[t];if(!n){if(16===t)return r;if(17===t)return new DataView(r);throw new Error("Could not find typed array for code "+t)}return new K[n](r)},g[120]=()=>{let e=A();return new RegExp(e[0],e[1])};const Y=[];function q(e){let t=n,g=l,p=c,d=h,y=s,b=u,m=o,S=new Uint8Array(r.slice(0,n)),I=i,A=i.slice(0,i.length),U=f,E=w,k=e();return n=t,l=g,c=p,h=d,s=y,u=b,o=m,r=S,w=E,(i=I).splice(0,i.length,...A),f=U,a=new DataView(r.buffer,r.byteOffset,r.byteLength),k}function Z(){r=null,u=null,i=null}g[98]=e=>{let t=(e[0]<<24)+(e[1]<<16)+(e[2]<<8)+e[3],r=l;return l+=t-e.length,o=Y,(o=[F(),F()]).position0=0,o.position1=0,o.postBundlePosition=l,l=r,A()},g[255]=e=>4==e.length?new Date(1e3*(16777216*e[0]+(e[1]<<16)+(e[2]<<8)+e[3])):8==e.length?new Date(((e[0]<<22)+(e[1]<<14)+(e[2]<<6)+(e[3]>>2))/1e6+1e3*(4294967296*(3&e[3])+16777216*e[4]+(e[5]<<16)+(e[6]<<8)+e[7])):12==e.length?new Date(((e[0]<<24)+(e[1]<<16)+(e[2]<<8)+e[3])/1e6+1e3*((128&e[4]?-281474976710656:0)+1099511627776*e[6]+4294967296*e[7]+16777216*e[8]+(e[9]<<16)+(e[10]<<8)+e[11])):new Date("invalid");const G=new Array(147);for(let e=0;e<256;e++)G[e]=+("1e"+Math.floor(45.15-.30103*e));const H=m;var Q=new m({useRecords:!1});const X=Q.unpack,ee=Q.unpackMultiple,te=Q.unpack,re={NEVER:0,ALWAYS:1,DECIMAL_ROUND:3,DECIMAL_FIT:4};let ne,ie,se,oe=new Float32Array(1),ue=new Uint8Array(oe.buffer,0,4);try{ne=new TextEncoder}catch(e){}const ae="undefined"!=typeof Buffer,le=ae?function(e){return Buffer.allocUnsafeSlow(e)}:Uint8Array,fe=ae?Buffer:Uint8Array,ce=ae?4294967296:2144337920;let he,ge,pe,de,ye=0,we=null;const be=/[\u0080-\uFFFF]/,me=Symbol("record-id");class Se extends m{constructor(e){let t,r,n,i;super(e),this.offset=0;let s=fe.prototype.utf8Write?function(e,t){return he.utf8Write(e,t,he.byteLength-t)}:!(!ne||!ne.encodeInto)&&function(e,t){return ne.encodeInto(e,he.subarray(t)).written},o=this;e||(e={});let u=e&&e.sequential,a=e.structures||e.saveStructures,l=e.maxSharedStructures;if(null==l&&(l=a?32:0),l>8160)throw new Error("Maximum maxSharedStructure is 8160");e.structuredClone&&null==e.moreTypes&&(this.moreTypes=!0);let f=e.maxOwnStructures;null==f&&(f=a?32:64),this.structures||0==e.useRecords||(this.structures=[]);let c=l>32||f+l>64,h=l+64,g=l+f+64;if(g>8256)throw new Error("Maximum maxSharedStructure + maxOwnStructure is 8192");let p=[],d=0,y=0;this.pack=this.encode=function(e,s){if(he||(he=new le(8192),pe=he.dataView||(he.dataView=new DataView(he.buffer,0,8192)),ye=0),de=he.length-10,de-ye<2048?(he=new le(he.length),pe=he.dataView||(he.dataView=new DataView(he.buffer,0,he.length)),de=he.length-10,ye=0):ye=ye+7&2147483640,t=ye,s&Ve&&(ye+=255&s),i=o.structuredClone?new Map:null,o.bundleStrings&&"string"!=typeof e?(we=[],we.size=1/0):we=null,n=o.structures,n){n.uninitialized&&(n=o._mergeStructures(o.getStructures()));let e=n.sharedLength||0;if(e>l)throw new Error("Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to "+n.sharedLength);if(!n.transitions){n.transitions=Object.create(null);for(let t=0;t<e;t++){let e=n[t];if(!e)continue;let r,i=n.transitions;for(let t=0,n=e.length;t<n;t++){let n=e[t];r=i[n],r||(r=i[n]=Object.create(null)),i=r}i[me]=t+64}this.lastNamedStructuresLength=e}u||(n.nextId=e+64)}let a;r&&(r=!1);try{o.randomAccessStructure&&e&&e.constructor&&e.constructor===Object?B(e):m(e);let u=we;if(we&&Ee(t,m,0),i&&i.idsToInsert){let e=i.idsToInsert.sort(((e,t)=>e.offset>t.offset?1:-1)),r=e.length,n=-1;for(;u&&r>0;){let i=e[--r].offset+t;i<u.stringsPosition+t&&-1===n&&(n=0),i>u.position+t?n>=0&&(n+=6):(n>=0&&(pe.setUint32(u.position+t,pe.getUint32(u.position+t)+n),n=-1),u=u.previous,r++)}n>=0&&u&&pe.setUint32(u.position+t,pe.getUint32(u.position+t)+n),ye+=6*e.length,ye>de&&E(ye),o.offset=ye;let s=function(e,t){let r,n=6*t.length,i=e.length-n;for(;r=t.pop();){let t=r.offset,s=r.id;e.copyWithin(t+n,t,i),n-=6;let o=t+n;e[o++]=214,e[o++]=105,e[o++]=s>>24,e[o++]=s>>16&255,e[o++]=s>>8&255,e[o++]=255&s,i=t}return e}(he.subarray(t,ye),e);return i=null,s}return o.offset=ye,s&je?(he.start=t,he.end=ye,he):he.subarray(t,ye)}catch(e){throw a=e,e}finally{if(n&&(w(),r&&o.saveStructures)){let r=n.sharedLength||0,i=he.subarray(t,ye),u=function(e,t){return e.isCompatible=e=>{let r=!e||(t.lastNamedStructuresLength||0)===e.length;return r||t._mergeStructures(e),r},e}(n,o);if(!a)return!1===o.saveStructures(u,u.isCompatible)?o.pack(e,s):(o.lastNamedStructuresLength=r,he.length>1073741824&&(he=null),i)}he.length>1073741824&&(he=null),s&De&&(ye=t)}};const w=()=>{y<10&&y++;let e=n.sharedLength||0;if(n.length>e&&!u&&(n.length=e),d>1e4)n.transitions=null,y=0,d=0,p.length>0&&(p=[]);else if(p.length>0&&!u){for(let e=0,t=p.length;e<t;e++)p[e][me]=0;p=[]}},b=e=>{var t=e.length;t<16?he[ye++]=144|t:t<65536?(he[ye++]=220,he[ye++]=t>>8,he[ye++]=255&t):(he[ye++]=221,pe.setUint32(ye,t),ye+=4);for(let r=0;r<t;r++)m(e[r])},m=e=>{ye>de&&(he=E(ye));var r,n=typeof e;if("string"===n){let n,i=e.length;if(we&&i>=4&&i<4096){if((we.size+=i)>21760){let e,r,n=(we[0]?3*we[0].length+we[1].length:0)+10;ye+n>de&&(he=E(ye+n)),we.position?(r=we,he[ye]=200,ye+=3,he[ye++]=98,e=ye-t,ye+=4,Ee(t,m,0),pe.setUint16(e+t-3,ye-t-e)):(he[ye++]=214,he[ye++]=98,e=ye-t,ye+=4),we=["",""],we.previous=r,we.size=0,we.position=e}let r=be.test(e);return we[r?0:1]+=e,he[ye++]=193,void m(r?-i:i)}n=i<32?1:i<256?2:i<65536?3:5;let o=3*i;if(ye+o>de&&(he=E(ye+o)),i<64||!s){let t,s,o,u=ye+n;for(t=0;t<i;t++)s=e.charCodeAt(t),s<128?he[u++]=s:s<2048?(he[u++]=s>>6|192,he[u++]=63&s|128):55296==(64512&s)&&56320==(64512&(o=e.charCodeAt(t+1)))?(s=65536+((1023&s)<<10)+(1023&o),t++,he[u++]=s>>18|240,he[u++]=s>>12&63|128,he[u++]=s>>6&63|128,he[u++]=63&s|128):(he[u++]=s>>12|224,he[u++]=s>>6&63|128,he[u++]=63&s|128);r=u-ye-n}else r=s(e,ye+n);r<32?he[ye++]=160|r:r<256?(n<2&&he.copyWithin(ye+2,ye+1,ye+1+r),he[ye++]=217,he[ye++]=r):r<65536?(n<3&&he.copyWithin(ye+3,ye+2,ye+2+r),he[ye++]=218,he[ye++]=r>>8,he[ye++]=255&r):(n<5&&he.copyWithin(ye+5,ye+3,ye+3+r),he[ye++]=219,pe.setUint32(ye,r),ye+=4),ye+=r}else if("number"===n)if(e>>>0===e)e<32||e<128&&!1===this.useRecords||e<64&&!this.randomAccessStructure?he[ye++]=e:e<256?(he[ye++]=204,he[ye++]=e):e<65536?(he[ye++]=205,he[ye++]=e>>8,he[ye++]=255&e):(he[ye++]=206,pe.setUint32(ye,e),ye+=4);else if(e>>0===e)e>=-32?he[ye++]=256+e:e>=-128?(he[ye++]=208,he[ye++]=e+256):e>=-32768?(he[ye++]=209,pe.setInt16(ye,e),ye+=2):(he[ye++]=210,pe.setInt32(ye,e),ye+=4);else{let t;if((t=this.useFloat32)>0&&e<4294967296&&e>=-2147483648){let r;if(he[ye++]=202,pe.setFloat32(ye,e),t<4||(r=e*G[(127&he[ye])<<1|he[ye+1]>>7])>>0===r)return void(ye+=4);ye--}he[ye++]=203,pe.setFloat64(ye,e),ye+=8}else if("object"===n||"function"===n)if(e){if(i){let r=i.get(e);if(r){if(!r.id){let e=i.idsToInsert||(i.idsToInsert=[]);r.id=e.push(r)}return he[ye++]=214,he[ye++]=112,pe.setUint32(ye,r.id),void(ye+=4)}i.set(e,{offset:ye-t})}let s=e.constructor;if(s===Object)U(e);else if(s===Array)b(e);else if(s===Map)if(this.mapAsEmptyObject)he[ye++]=128;else{(r=e.size)<16?he[ye++]=128|r:r<65536?(he[ye++]=222,he[ye++]=r>>8,he[ye++]=255&r):(he[ye++]=223,pe.setUint32(ye,r),ye+=4);for(let[t,r]of e)m(t),m(r)}else{for(let t=0,r=ie.length;t<r;t++){if(e instanceof se[t]){let r=ie[t];if(r.write){r.type&&(he[ye++]=212,he[ye++]=r.type,he[ye++]=0);let t=r.write.call(this,e);return void(t===e?Array.isArray(e)?b(e):U(e):m(t))}let n,i=he,s=pe,o=ye;he=null;try{n=r.pack.call(this,e,(e=>(he=i,i=null,ye+=e,ye>de&&E(ye),{target:he,targetView:pe,position:ye-e})),m)}finally{i&&(he=i,pe=s,ye=o,de=he.length-10)}return void(n&&(n.length+ye>de&&E(n.length+ye),ye=Ue(n,he,ye,r.type)))}}if(Array.isArray(e))b(e);else{if(e.toJSON){const t=e.toJSON();if(t!==e)return m(t)}if("function"===n)return m(this.writeFunction&&this.writeFunction(e));U(e)}}}else he[ye++]=192;else if("boolean"===n)he[ye++]=e?195:194;else if("bigint"===n){if(e<0x8000000000000000&&e>=-0x8000000000000000)he[ye++]=211,pe.setBigInt64(ye,e);else if(e<0x10000000000000000&&e>0)he[ye++]=207,pe.setBigUint64(ye,e);else{if(!this.largeBigIntToFloat){if(this.largeBigIntToString)return m(e.toString());if((this.useBigIntExtension||this.moreTypes)&&e<BigInt(2)**BigInt(1023)&&e>-(BigInt(2)**BigInt(1023))){he[ye++]=199,ye++,he[ye++]=66;let t,r=[];do{let n=e&BigInt(255);t=(n&BigInt(128))===(e<BigInt(0)?BigInt(128):BigInt(0)),r.push(n),e>>=BigInt(8)}while(e!==BigInt(0)&&e!==BigInt(-1)||!t);he[ye-2]=r.length;for(let e=r.length;e>0;)he[ye++]=Number(r[--e]);return}throw new RangeError(e+" was too large to fit in MessagePack 64-bit integer format, use useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set largeBigIntToString to convert to string")}he[ye++]=203,pe.setFloat64(ye,Number(e))}ye+=8}else{if("undefined"!==n)throw new Error("Unknown type: "+n);this.encodeUndefinedAsNil?he[ye++]=192:(he[ye++]=212,he[ye++]=0,he[ye++]=0)}},S=this.variableMapSize||this.coercibleKeyAsNumber||this.skipValues?e=>{let t;if(this.skipValues){t=[];for(let r in e)"function"==typeof e.hasOwnProperty&&!e.hasOwnProperty(r)||this.skipValues.includes(e[r])||t.push(r)}else t=Object.keys(e);let r,n=t.length;if(n<16?he[ye++]=128|n:n<65536?(he[ye++]=222,he[ye++]=n>>8,he[ye++]=255&n):(he[ye++]=223,pe.setUint32(ye,n),ye+=4),this.coercibleKeyAsNumber)for(let i=0;i<n;i++){r=t[i];let n=Number(r);m(isNaN(n)?r:n),m(e[r])}else for(let i=0;i<n;i++)m(r=t[i]),m(e[r])}:e=>{he[ye++]=222;let r=ye-t;ye+=2;let n=0;for(let t in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(t))&&(m(t),m(e[t]),n++);if(n>65535)throw new Error('Object is too large to serialize with fast 16-bit map size, use the "variableMapSize" option to serialize this object');he[r+++t]=n>>8,he[r+t]=255&n},I=!1===this.useRecords?S:e.progressiveRecords&&!c?e=>{let r,i,s=n.transitions||(n.transitions=Object.create(null)),o=ye++-t;for(let u in e)if("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(u)){if(r=s[u],r)s=r;else{let a=Object.keys(e),l=s;s=n.transitions;let f=0;for(let e=0,t=a.length;e<t;e++){let t=a[e];r=s[t],r||(r=s[t]=Object.create(null),f++),s=r}o+t+1==ye?(ye--,k(s,a,f)):v(s,a,o,f),i=!0,s=l[u]}m(e[u])}if(!i){let r=s[me];r?he[o+t]=r:v(s,Object.keys(e),o,0)}}:e=>{let t,r=n.transitions||(n.transitions=Object.create(null)),i=0;for(let n in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(n))&&(t=r[n],t||(t=r[n]=Object.create(null),i++),r=t);let s=r[me];s?s>=96&&c?(he[ye++]=96+(31&(s-=96)),he[ye++]=s>>5):he[ye++]=s:k(r,r.__keys__||Object.keys(e),i);for(let t in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(t))&&m(e[t])},A="function"==typeof this.useRecords&&this.useRecords,U=A?e=>{A(e)?I(e):S(e)}:I,E=e=>{let r;if(e>16777216){if(e-t>ce)throw new Error("Packed buffer would be larger than maximum buffer size");r=Math.min(ce,4096*Math.round(Math.max((e-t)*(e>67108864?1.25:2),4194304)/4096))}else r=1+(Math.max(e-t<<2,he.length-1)>>12)<<12;let n=new le(r);return pe=n.dataView||(n.dataView=new DataView(n.buffer,0,r)),e=Math.min(e,he.length),he.copy?he.copy(n,0,t,e):n.set(he.slice(t,e)),ye-=t,t=0,de=n.length-10,he=n},k=(e,t,i)=>{let s=n.nextId;s||(s=64),s<h&&this.shouldShareStructure&&!this.shouldShareStructure(t)?(s=n.nextOwnId,s<g||(s=h),n.nextOwnId=s+1):(s>=g&&(s=h),n.nextId=s+1);let o=t.highByte=s>=96&&c?s-96>>5:-1;e[me]=s,e.__keys__=t,n[s-64]=t,s<h?(t.isShared=!0,n.sharedLength=s-63,r=!0,o>=0?(he[ye++]=96+(31&s),he[ye++]=o):he[ye++]=s):(o>=0?(he[ye++]=213,he[ye++]=114,he[ye++]=96+(31&s),he[ye++]=o):(he[ye++]=212,he[ye++]=114,he[ye++]=s),i&&(d+=y*i),p.length>=f&&(p.shift()[me]=0),p.push(e),m(t))},v=(e,r,n,i)=>{let s=he,o=ye,u=de,a=t;he=ge,ye=0,t=0,he||(ge=he=new le(8192)),de=he.length-10,k(e,r,i),ge=he;let l=ye;if(he=s,ye=o,de=u,t=a,l>1){let e=ye+l-1;e>de&&E(e);let r=n+t;he.copyWithin(r+l,r+1,ye),he.set(ge.slice(0,l),r),ye=e}else he[n+t]=ge[0]},B=e=>{let i=undefined(e,he,t,ye,n,E,((e,t,n)=>{if(n)return r=!0;ye=t;let i=he;return m(e),w(),i!==he?{position:ye,targetView:pe,target:he}:ye}),this);if(0===i)return U(e);ye=i}}useBuffer(e){he=e,he.dataView||(he.dataView=new DataView(he.buffer,he.byteOffset,he.byteLength)),ye=0}set position(e){ye=e}get position(){return ye}clearSharedData(){this.structures&&(this.structures=[]),this.typedStructs&&(this.typedStructs=[])}}function Ie(e,t,r,n){let i=e.byteLength;if(i+1<256){var{target:s,position:o}=r(4+i);s[o++]=199,s[o++]=i+1}else if(i+1<65536){var{target:s,position:o}=r(5+i);s[o++]=200,s[o++]=i+1>>8,s[o++]=i+1&255}else{var{target:s,position:o,targetView:u}=r(7+i);s[o++]=201,u.setUint32(o,i+1),o+=4}s[o++]=116,s[o++]=t,e.buffer||(e=new Uint8Array(e)),s.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),o)}function Ae(e,t){let r=e.byteLength;var n,i;if(r<256){var{target:n,position:i}=t(r+2);n[i++]=196,n[i++]=r}else if(r<65536){var{target:n,position:i}=t(r+3);n[i++]=197,n[i++]=r>>8,n[i++]=255&r}else{var{target:n,position:i,targetView:s}=t(r+5);n[i++]=198,s.setUint32(i,r),i+=4}n.set(e,i)}function Ue(e,t,r,n){let i=e.length;switch(i){case 1:t[r++]=212;break;case 2:t[r++]=213;break;case 4:t[r++]=214;break;case 8:t[r++]=215;break;case 16:t[r++]=216;break;default:i<256?(t[r++]=199,t[r++]=i):i<65536?(t[r++]=200,t[r++]=i>>8,t[r++]=255&i):(t[r++]=201,t[r++]=i>>24,t[r++]=i>>16&255,t[r++]=i>>8&255,t[r++]=255&i)}return t[r++]=n,t.set(e,r),r+=i}function Ee(e,t,r){if(we.length>0){pe.setUint32(we.position+e,ye+r-we.position-e),we.stringsPosition=ye-e;let n=we;we=null,t(n[0]),t(n[1])}}se=[Date,Set,Error,RegExp,ArrayBuffer,Object.getPrototypeOf(Uint8Array.prototype).constructor,DataView,d],ie=[{pack(e,t,r){let n=e.getTime()/1e3;if((this.useTimestamp32||0===e.getMilliseconds())&&n>=0&&n<4294967296){let{target:e,targetView:r,position:i}=t(6);e[i++]=214,e[i++]=255,r.setUint32(i,n)}else if(n>0&&n<4294967296){let{target:r,targetView:i,position:s}=t(10);r[s++]=215,r[s++]=255,i.setUint32(s,4e6*e.getMilliseconds()+(n/1e3/4294967296>>0)),i.setUint32(s+4,n)}else if(isNaN(n)){if(this.onInvalidDate)return t(0),r(this.onInvalidDate());let{target:e,targetView:n,position:i}=t(3);e[i++]=212,e[i++]=255,e[i++]=255}else{let{target:r,targetView:i,position:s}=t(15);r[s++]=199,r[s++]=12,r[s++]=255,i.setUint32(s,1e6*e.getMilliseconds()),i.setBigInt64(s+4,BigInt(Math.floor(n)))}}},{pack(e,t,r){if(this.setAsEmptyObject)return t(0),r({});let n=Array.from(e),{target:i,position:s}=t(this.moreTypes?3:0);this.moreTypes&&(i[s++]=212,i[s++]=115,i[s++]=0),r(n)}},{pack(e,t,r){let{target:n,position:i}=t(this.moreTypes?3:0);this.moreTypes&&(n[i++]=212,n[i++]=101,n[i++]=0),r([e.name,e.message,e.cause])}},{pack(e,t,r){let{target:n,position:i}=t(this.moreTypes?3:0);this.moreTypes&&(n[i++]=212,n[i++]=120,n[i++]=0),r([e.source,e.flags])}},{pack(e,t){this.moreTypes?Ie(e,16,t):Ae(ae?Buffer.from(e):new Uint8Array(e),t)}},{pack(e,t){let r=e.constructor;r!==fe&&this.moreTypes?Ie(e,$.indexOf(r.name),t):Ae(e,t)}},{pack(e,t){this.moreTypes?Ie(e,17,t):Ae(ae?Buffer.from(e):new Uint8Array(e),t)}},{pack(e,t){let{target:r,position:n}=t(1);r[n]=193}}];let ke=new Se({useRecords:!1});const ve=ke.pack,Be=ke.pack,Oe=Se,{NEVER:_e,ALWAYS:xe,DECIMAL_ROUND:Te,DECIMAL_FIT:Me}=re,je=512,De=1024,Ve=2048;const Re=function(e,t={}){if(!e||"object"!=typeof e)throw new Error("first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise");const r=new m(t);let n;const i=e=>{let t;n&&(e=Buffer.concat([n,e]),n=void 0);try{t=r.unpackMultiple(e)}catch(r){if(!r.incomplete)throw r;n=e.slice(r.lastPosition),t=r.values}return t};return"function"==typeof e[Symbol.iterator]?function*(){for(const t of e)yield*i(t)}():"function"==typeof e[Symbol.asyncIterator]?async function*(){for await(const t of e)yield*i(t)}():void 0},Fe=function(e,t={}){if(e&&"object"==typeof e){if("function"==typeof e[Symbol.iterator])return function*(e,t){const r=new Se(t);for(const t of e)yield r.pack(t)}(e,t);if("function"==typeof e.then||"function"==typeof e[Symbol.asyncIterator])return async function*(e,t){const r=new Se(t);for await(const t of e)yield r.pack(t)}(e,t);throw new Error("first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise")}throw new Error("first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable")};e.ALWAYS=xe,e.C1=y,e.DECIMAL_FIT=Me,e.DECIMAL_ROUND=Te,e.Decoder=H,e.Encoder=Oe,e.FLOAT32_OPTIONS=re,e.NEVER=_e,e.Packr=Se,e.RESERVE_START_SPACE=Ve,e.RESET_BUFFER_MODE=De,e.REUSE_BUFFER_MODE=je,e.Unpackr=m,e.addExtension=function(e){if(e.Class){if(!e.pack&&!e.write)throw new Error("Extension has no pack or write function");if(e.pack&&!e.type)throw new Error("Extension has no type (numeric code to identify the extension)");se.unshift(e.Class),ie.unshift(e)}!function(e){e.unpack?g[e.type]=e.unpack:g[e.type]=e}(e)},e.clearSource=Z,e.decode=te,e.decodeIter=Re,e.encode=Be,e.encodeIter=Fe,e.isNativeAccelerationEnabled=!1,e.mapsAsObjects=!0,e.pack=ve,e.roundFloat32=function(e){oe[0]=e;let t=G[(127&ue[3])<<1|ue[2]>>7];return(t*e+(e>0?.5:-.5)>>0)/t},e.unpack=X,e.unpackMultiple=ee,e.useRecords=!1}));
//# sourceMappingURL=index.min.js.map
