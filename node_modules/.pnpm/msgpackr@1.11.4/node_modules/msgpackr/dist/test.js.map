{"version": 3, "file": "test.js", "sources": ["../unpack.js", "../pack.js", "../struct.js", "../node-index.js", "../tests/test.js"], "sourcesContent": ["var decoder\ntry {\n\tdecoder = new TextDecoder()\n} catch(error) {}\nvar src\nvar srcEnd\nvar position = 0\nvar alreadySet\nconst EMPTY_ARRAY = []\nvar strings = EMPTY_ARRAY\nvar stringPosition = 0\nvar currentUnpackr = {}\nvar currentStructures\nvar srcString\nvar srcStringStart = 0\nvar srcStringEnd = 0\nvar bundledStrings\nvar referenceMap\nvar currentExtensions = []\nvar dataView\nvar defaultOptions = {\n\tuseRecords: false,\n\tmapsAsObjects: true\n}\nexport class C1Type {}\nexport const C1 = new C1Type()\nC1.name = 'MessagePack 0xC1'\nvar sequentialMode = false\nvar inlineObjectReadThreshold = 2\nvar readStruct, onLoadedStructures, onSaveState\nvar BlockedFunction // we use search and replace to change the next call to BlockedFunction to avoid CSP issues for\n// no-eval build\ntry {\n\tnew Function('')\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n\tinlineObjectReadThreshold = Infinity\n}\n\nexport class Unpackr {\n\tconstructor(options) {\n\t\tif (options) {\n\t\t\tif (options.useRecords === false && options.mapsAsObjects === undefined)\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\tif (options.sequential && options.trusted !== false) {\n\t\t\t\toptions.trusted = true;\n\t\t\t\tif (!options.structures && options.useRecords != false) {\n\t\t\t\t\toptions.structures = []\n\t\t\t\t\tif (!options.maxSharedStructures)\n\t\t\t\t\t\toptions.maxSharedStructures = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (options.structures)\n\t\t\t\toptions.structures.sharedLength = options.structures.length\n\t\t\telse if (options.getStructures) {\n\t\t\t\t(options.structures = []).uninitialized = true // this is what we use to denote an uninitialized structures\n\t\t\t\toptions.structures.sharedLength = 0\n\t\t\t}\n\t\t\tif (options.int64AsNumber) {\n\t\t\t\toptions.int64AsType = 'number'\n\t\t\t}\n\t\t}\n\t\tObject.assign(this, options)\n\t}\n\tunpack(source, options) {\n\t\tif (src) {\n\t\t\t// re-entrant execution, save the state and restore it after we do this unpack\n\t\t\treturn saveState(() => {\n\t\t\t\tclearSource()\n\t\t\t\treturn this ? this.unpack(source, options) : Unpackr.prototype.unpack.call(defaultOptions, source, options)\n\t\t\t})\n\t\t}\n\t\tif (!source.buffer && source.constructor === ArrayBuffer)\n\t\t\tsource = typeof Buffer !== 'undefined' ? Buffer.from(source) : new Uint8Array(source);\n\t\tif (typeof options === 'object') {\n\t\t\tsrcEnd = options.end || source.length\n\t\t\tposition = options.start || 0\n\t\t} else {\n\t\t\tposition = 0\n\t\t\tsrcEnd = options > -1 ? options : source.length\n\t\t}\n\t\tstringPosition = 0\n\t\tsrcStringEnd = 0\n\t\tsrcString = null\n\t\tstrings = EMPTY_ARRAY\n\t\tbundledStrings = null\n\t\tsrc = source\n\t\t// this provides cached access to the data view for a buffer if it is getting reused, which is a recommend\n\t\t// technique for getting data from a database where it can be copied into an existing buffer instead of creating\n\t\t// new ones\n\t\ttry {\n\t\t\tdataView = source.dataView || (source.dataView = new DataView(source.buffer, source.byteOffset, source.byteLength))\n\t\t} catch(error) {\n\t\t\t// if it doesn't have a buffer, maybe it is the wrong type of object\n\t\t\tsrc = null\n\t\t\tif (source instanceof Uint8Array)\n\t\t\t\tthrow error\n\t\t\tthrow new Error('Source must be a Uint8Array or Buffer but was a ' + ((source && typeof source == 'object') ? source.constructor.name : typeof source))\n\t\t}\n\t\tif (this instanceof Unpackr) {\n\t\t\tcurrentUnpackr = this\n\t\t\tif (this.structures) {\n\t\t\t\tcurrentStructures = this.structures\n\t\t\t\treturn checkedRead(options)\n\t\t\t} else if (!currentStructures || currentStructures.length > 0) {\n\t\t\t\tcurrentStructures = []\n\t\t\t}\n\t\t} else {\n\t\t\tcurrentUnpackr = defaultOptions\n\t\t\tif (!currentStructures || currentStructures.length > 0)\n\t\t\t\tcurrentStructures = []\n\t\t}\n\t\treturn checkedRead(options)\n\t}\n\tunpackMultiple(source, forEach) {\n\t\tlet values, lastPosition = 0\n\t\ttry {\n\t\t\tsequentialMode = true\n\t\t\tlet size = source.length\n\t\t\tlet value = this ? this.unpack(source, size) : defaultUnpackr.unpack(source, size)\n\t\t\tif (forEach) {\n\t\t\t\tif (forEach(value, lastPosition, position) === false) return;\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tif (forEach(checkedRead(), lastPosition, position) === false) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalues = [ value ]\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tvalues.push(checkedRead())\n\t\t\t\t}\n\t\t\t\treturn values\n\t\t\t}\n\t\t} catch(error) {\n\t\t\terror.lastPosition = lastPosition\n\t\t\terror.values = values\n\t\t\tthrow error\n\t\t} finally {\n\t\t\tsequentialMode = false\n\t\t\tclearSource()\n\t\t}\n\t}\n\t_mergeStructures(loadedStructures, existingStructures) {\n\t\tif (onLoadedStructures)\n\t\t\tloadedStructures = onLoadedStructures.call(this, loadedStructures);\n\t\tloadedStructures = loadedStructures || []\n\t\tif (Object.isFrozen(loadedStructures))\n\t\t\tloadedStructures = loadedStructures.map(structure => structure.slice(0))\n\t\tfor (let i = 0, l = loadedStructures.length; i < l; i++) {\n\t\t\tlet structure = loadedStructures[i]\n\t\t\tif (structure) {\n\t\t\t\tstructure.isShared = true\n\t\t\t\tif (i >= 32)\n\t\t\t\t\tstructure.highByte = (i - 32) >> 5\n\t\t\t}\n\t\t}\n\t\tloadedStructures.sharedLength = loadedStructures.length\n\t\tfor (let id in existingStructures || []) {\n\t\t\tif (id >= 0) {\n\t\t\t\tlet structure = loadedStructures[id]\n\t\t\t\tlet existing = existingStructures[id]\n\t\t\t\tif (existing) {\n\t\t\t\t\tif (structure)\n\t\t\t\t\t\t(loadedStructures.restoreStructures || (loadedStructures.restoreStructures = []))[id] = structure\n\t\t\t\t\tloadedStructures[id] = existing\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this.structures = loadedStructures\n\t}\n\tdecode(source, options) {\n\t\treturn this.unpack(source, options)\n\t}\n}\nexport function getPosition() {\n\treturn position\n}\nexport function checkedRead(options) {\n\ttry {\n\t\tif (!currentUnpackr.trusted && !sequentialMode) {\n\t\t\tlet sharedLength = currentStructures.sharedLength || 0\n\t\t\tif (sharedLength < currentStructures.length)\n\t\t\t\tcurrentStructures.length = sharedLength\n\t\t}\n\t\tlet result\n\t\tif (currentUnpackr.randomAccessStructure && src[position] < 0x40 && src[position] >= 0x20 && readStruct) {\n\t\t\tresult = readStruct(src, position, srcEnd, currentUnpackr)\n\t\t\tsrc = null // dispose of this so that recursive unpack calls don't save state\n\t\t\tif (!(options && options.lazy) && result)\n\t\t\t\tresult = result.toJSON()\n\t\t\tposition = srcEnd\n\t\t} else\n\t\t\tresult = read()\n\t\tif (bundledStrings) { // bundled strings to skip past\n\t\t\tposition = bundledStrings.postBundlePosition\n\t\t\tbundledStrings = null\n\t\t}\n\t\tif (sequentialMode)\n\t\t\t// we only need to restore the structures if there was an error, but if we completed a read,\n\t\t\t// we can clear this out and keep the structures we read\n\t\t\tcurrentStructures.restoreStructures = null\n\n\t\tif (position == srcEnd) {\n\t\t\t// finished reading this source, cleanup references\n\t\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\t\trestoreStructures()\n\t\t\tcurrentStructures = null\n\t\t\tsrc = null\n\t\t\tif (referenceMap)\n\t\t\t\treferenceMap = null\n\t\t} else if (position > srcEnd) {\n\t\t\t// over read\n\t\t\tthrow new Error('Unexpected end of MessagePack data')\n\t\t} else if (!sequentialMode) {\n\t\t\tlet jsonView;\n\t\t\ttry {\n\t\t\t\tjsonView = JSON.stringify(result, (_, value) => typeof value === \"bigint\" ? `${value}n` : value).slice(0, 100)\n\t\t\t} catch(error) {\n\t\t\t\tjsonView = '(JSON view not available ' + error + ')'\n\t\t\t}\n\t\t\tthrow new Error('Data read, but end of buffer not reached ' + jsonView)\n\t\t}\n\t\t// else more to read, but we are reading sequentially, so don't clear source yet\n\t\treturn result\n\t} catch(error) {\n\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\trestoreStructures()\n\t\tclearSource()\n\t\tif (error instanceof RangeError || error.message.startsWith('Unexpected end of buffer') || position > srcEnd) {\n\t\t\terror.incomplete = true\n\t\t}\n\t\tthrow error\n\t}\n}\n\nfunction restoreStructures() {\n\tfor (let id in currentStructures.restoreStructures) {\n\t\tcurrentStructures[id] = currentStructures.restoreStructures[id]\n\t}\n\tcurrentStructures.restoreStructures = null\n}\n\nexport function read() {\n\tlet token = src[position++]\n\tif (token < 0xa0) {\n\t\tif (token < 0x80) {\n\t\t\tif (token < 0x40)\n\t\t\t\treturn token\n\t\t\telse {\n\t\t\t\tlet structure = currentStructures[token & 0x3f] ||\n\t\t\t\t\tcurrentUnpackr.getStructures && loadStructures()[token & 0x3f]\n\t\t\t\tif (structure) {\n\t\t\t\t\tif (!structure.read) {\n\t\t\t\t\t\tstructure.read = createStructureReader(structure, token & 0x3f)\n\t\t\t\t\t}\n\t\t\t\t\treturn structure.read()\n\t\t\t\t} else\n\t\t\t\t\treturn token\n\t\t\t}\n\t\t} else if (token < 0x90) {\n\t\t\t// map\n\t\t\ttoken -= 0x80\n\t\t\tif (currentUnpackr.mapsAsObjects) {\n\t\t\t\tlet object = {}\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tlet key = readKey()\n\t\t\t\t\tif (key === '__proto__')\n\t\t\t\t\t\tkey = '__proto_'\n\t\t\t\t\tobject[key] = read()\n\t\t\t\t}\n\t\t\t\treturn object\n\t\t\t} else {\n\t\t\t\tlet map = new Map()\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tmap.set(read(), read())\n\t\t\t\t}\n\t\t\t\treturn map\n\t\t\t}\n\t\t} else {\n\t\t\ttoken -= 0x90\n\t\t\tlet array = new Array(token)\n\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\tarray[i] = read()\n\t\t\t}\n\t\t\tif (currentUnpackr.freezeData)\n\t\t\t\treturn Object.freeze(array)\n\t\t\treturn array\n\t\t}\n\t} else if (token < 0xc0) {\n\t\t// fixstr\n\t\tlet length = token - 0xa0\n\t\tif (srcStringEnd >= position) {\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\t}\n\t\tif (srcStringEnd == 0 && srcEnd < 140) {\n\t\t\t// for small blocks, avoiding the overhead of the extract call is helpful\n\t\t\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\t\t\tif (string != null)\n\t\t\t\treturn string\n\t\t}\n\t\treturn readFixedString(length)\n\t} else {\n\t\tlet value\n\t\tswitch (token) {\n\t\t\tcase 0xc0: return null\n\t\t\tcase 0xc1:\n\t\t\t\tif (bundledStrings) {\n\t\t\t\t\tvalue = read() // followed by the length of the string in characters (not bytes!)\n\t\t\t\t\tif (value > 0)\n\t\t\t\t\t\treturn bundledStrings[1].slice(bundledStrings.position1, bundledStrings.position1 += value)\n\t\t\t\t\telse\n\t\t\t\t\t\treturn bundledStrings[0].slice(bundledStrings.position0, bundledStrings.position0 -= value)\n\t\t\t\t}\n\t\t\t\treturn C1; // \"never-used\", return special object to denote that\n\t\t\tcase 0xc2: return false\n\t\t\tcase 0xc3: return true\n\t\t\tcase 0xc4:\n\t\t\t\t// bin 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value === undefined)\n\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc5:\n\t\t\t\t// bin 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc6:\n\t\t\t\t// bin 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc7:\n\t\t\t\t// ext 8\n\t\t\t\treturn readExt(src[position++])\n\t\t\tcase 0xc8:\n\t\t\t\t// ext 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xc9:\n\t\t\t\t// ext 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xca:\n\t\t\t\tvalue = dataView.getFloat32(position)\n\t\t\t\tif (currentUnpackr.useFloat32 > 2) {\n\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\tlet multiplier = mult10[((src[position] & 0x7f) << 1) | (src[position + 1] >> 7)]\n\t\t\t\t\tposition += 4\n\t\t\t\t\treturn ((multiplier * value + (value > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n\t\t\t\t}\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcb:\n\t\t\t\tvalue = dataView.getFloat64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\t\t\t// uint handlers\n\t\t\tcase 0xcc:\n\t\t\t\treturn src[position++]\n\t\t\tcase 0xcd:\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xce:\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcf:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getUint32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\t\tif (value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\t// int handlers\n\t\t\tcase 0xd0:\n\t\t\t\treturn dataView.getInt8(position++)\n\t\t\tcase 0xd1:\n\t\t\t\tvalue = dataView.getInt16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xd2:\n\t\t\t\tvalue = dataView.getInt32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xd3:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getInt32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\t\tif (value>=BigInt(-2)<<BigInt(52)&&value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\tcase 0xd4:\n\t\t\t\t// fixext 1\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f)\n\t\t\t\t} else {\n\t\t\t\t\tlet extension = currentExtensions[value]\n\t\t\t\t\tif (extension) {\n\t\t\t\t\t\tif (extension.read) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension.read(read())\n\t\t\t\t\t\t} else if (extension.noBuffer) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension()\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treturn extension(src.subarray(position, ++position))\n\t\t\t\t\t} else\n\t\t\t\t\t\tthrow new Error('Unknown extension ' + value)\n\t\t\t\t}\n\t\t\tcase 0xd5:\n\t\t\t\t// fixext 2\n\t\t\t\tvalue = src[position]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\tposition++\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f, src[position++])\n\t\t\t\t} else\n\t\t\t\t\treturn readExt(2)\n\t\t\tcase 0xd6:\n\t\t\t\t// fixext 4\n\t\t\t\treturn readExt(4)\n\t\t\tcase 0xd7:\n\t\t\t\t// fixext 8\n\t\t\t\treturn readExt(8)\n\t\t\tcase 0xd8:\n\t\t\t\t// fixext 16\n\t\t\t\treturn readExt(16)\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString8(value)\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString16(value)\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString32(value)\n\t\t\tcase 0xdc:\n\t\t\t// array 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xdd:\n\t\t\t// array 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xde:\n\t\t\t// map 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readMap(value)\n\t\t\tcase 0xdf:\n\t\t\t// map 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readMap(value)\n\t\t\tdefault: // negative int\n\t\t\t\tif (token >= 0xe0)\n\t\t\t\t\treturn token - 0x100\n\t\t\t\tif (token === undefined) {\n\t\t\t\t\tlet error = new Error('Unexpected end of MessagePack data')\n\t\t\t\t\terror.incomplete = true\n\t\t\t\t\tthrow error\n\t\t\t\t}\n\t\t\t\tthrow new Error('Unknown MessagePack token ' + token)\n\n\t\t}\n\t}\n}\nconst validName = /^[a-zA-Z_$][a-zA-Z\\d_$]*$/\nfunction createStructureReader(structure, firstId) {\n\tfunction readObject() {\n\t\t// This initial function is quick to instantiate, but runs slower. After several iterations pay the cost to build the faster function\n\t\tif (readObject.count++ > inlineObjectReadThreshold) {\n\t\t\tlet readObject = structure.read = (new Function('r', 'return function(){return ' + (currentUnpackr.freezeData ? 'Object.freeze' : '') +\n\t\t\t\t'({' + structure.map(key => key === '__proto__' ? '__proto_:r()' : validName.test(key) ? key + ':r()' : ('[' + JSON.stringify(key) + ']:r()')).join(',') + '})}'))(read)\n\t\t\tif (structure.highByte === 0)\n\t\t\t\tstructure.read = createSecondByteReader(firstId, structure.read)\n\t\t\treturn readObject() // second byte is already read, if there is one so immediately read object\n\t\t}\n\t\tlet object = {}\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet key = structure[i]\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_'\n\t\t\tobject[key] = read()\n\t\t}\n\t\tif (currentUnpackr.freezeData)\n\t\t\treturn Object.freeze(object);\n\t\treturn object\n\t}\n\treadObject.count = 0\n\tif (structure.highByte === 0) {\n\t\treturn createSecondByteReader(firstId, readObject)\n\t}\n\treturn readObject\n}\n\nconst createSecondByteReader = (firstId, read0) => {\n\treturn function() {\n\t\tlet highByte = src[position++]\n\t\tif (highByte === 0)\n\t\t\treturn read0()\n\t\tlet id = firstId < 32 ? -(firstId + (highByte << 5)) : firstId + (highByte << 5)\n\t\tlet structure = currentStructures[id] || loadStructures()[id]\n\t\tif (!structure) {\n\t\t\tthrow new Error('Record id is not defined for ' + id)\n\t\t}\n\t\tif (!structure.read)\n\t\t\tstructure.read = createStructureReader(structure, firstId)\n\t\treturn structure.read()\n\t}\n}\n\nexport function loadStructures() {\n\tlet loadedStructures = saveState(() => {\n\t\t// save the state in case getStructures modifies our buffer\n\t\tsrc = null\n\t\treturn currentUnpackr.getStructures()\n\t})\n\treturn currentStructures = currentUnpackr._mergeStructures(loadedStructures, currentStructures)\n}\n\nvar readFixedString = readStringJS\nvar readString8 = readStringJS\nvar readString16 = readStringJS\nvar readString32 = readStringJS\nexport let isNativeAccelerationEnabled = false\n\nexport function setExtractor(extractStrings) {\n\tisNativeAccelerationEnabled = true\n\treadFixedString = readString(1)\n\treadString8 = readString(2)\n\treadString16 = readString(3)\n\treadString32 = readString(5)\n\tfunction readString(headerLength) {\n\t\treturn function readString(length) {\n\t\t\tlet string = strings[stringPosition++]\n\t\t\tif (string == null) {\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\treturn readStringJS(length)\n\t\t\t\tlet byteOffset = src.byteOffset\n\t\t\t\tlet extraction = extractStrings(position - headerLength + byteOffset, srcEnd + byteOffset, src.buffer)\n\t\t\t\tif (typeof extraction == 'string') {\n\t\t\t\t\tstring = extraction\n\t\t\t\t\tstrings = EMPTY_ARRAY\n\t\t\t\t} else {\n\t\t\t\t\tstrings = extraction\n\t\t\t\t\tstringPosition = 1\n\t\t\t\t\tsrcStringEnd = 1 // even if a utf-8 string was decoded, must indicate we are in the midst of extracted strings and can't skip strings\n\t\t\t\t\tstring = strings[0]\n\t\t\t\t\tif (string === undefined)\n\t\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet srcStringLength = string.length\n\t\t\tif (srcStringLength <= length) {\n\t\t\t\tposition += length\n\t\t\t\treturn string\n\t\t\t}\n\t\t\tsrcString = string\n\t\t\tsrcStringStart = position\n\t\t\tsrcStringEnd = position + srcStringLength\n\t\t\tposition += length\n\t\t\treturn string.slice(0, length) // we know we just want the beginning\n\t\t}\n\t}\n}\nfunction readStringJS(length) {\n\tlet result\n\tif (length < 16) {\n\t\tif (result = shortStringInJS(length))\n\t\t\treturn result\n\t}\n\tif (length > 64 && decoder)\n\t\treturn decoder.decode(src.subarray(position, position += length))\n\tconst end = position + length\n\tconst units = []\n\tresult = ''\n\twhile (position < end) {\n\t\tconst byte1 = src[position++]\n\t\tif ((byte1 & 0x80) === 0) {\n\t\t\t// 1 byte\n\t\t\tunits.push(byte1)\n\t\t} else if ((byte1 & 0xe0) === 0xc0) {\n\t\t\t// 2 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 6) | byte2)\n\t\t} else if ((byte1 & 0xf0) === 0xe0) {\n\t\t\t// 3 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3)\n\t\t} else if ((byte1 & 0xf8) === 0xf0) {\n\t\t\t// 4 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tconst byte4 = src[position++] & 0x3f\n\t\t\tlet unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4\n\t\t\tif (unit > 0xffff) {\n\t\t\t\tunit -= 0x10000\n\t\t\t\tunits.push(((unit >>> 10) & 0x3ff) | 0xd800)\n\t\t\t\tunit = 0xdc00 | (unit & 0x3ff)\n\t\t\t}\n\t\t\tunits.push(unit)\n\t\t} else {\n\t\t\tunits.push(byte1)\n\t\t}\n\n\t\tif (units.length >= 0x1000) {\n\t\t\tresult += fromCharCode.apply(String, units)\n\t\t\tunits.length = 0\n\t\t}\n\t}\n\n\tif (units.length > 0) {\n\t\tresult += fromCharCode.apply(String, units)\n\t}\n\n\treturn result\n}\nexport function readString(source, start, length) {\n\tlet existingSrc = src;\n\tsrc = source;\n\tposition = start;\n\ttry {\n\t\treturn readStringJS(length);\n\t} finally {\n\t\tsrc = existingSrc;\n\t}\n}\n\nfunction readArray(length) {\n\tlet array = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tarray[i] = read()\n\t}\n\tif (currentUnpackr.freezeData)\n\t\treturn Object.freeze(array)\n\treturn array\n}\n\nfunction readMap(length) {\n\tif (currentUnpackr.mapsAsObjects) {\n\t\tlet object = {}\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tlet key = readKey()\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tobject[key] = read()\n\t\t}\n\t\treturn object\n\t} else {\n\t\tlet map = new Map()\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tmap.set(read(), read())\n\t\t}\n\t\treturn map\n\t}\n}\n\nvar fromCharCode = String.fromCharCode\nfunction longStringInJS(length) {\n\tlet start = position\n\tlet bytes = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tconst byte = src[position++];\n\t\tif ((byte & 0x80) > 0) {\n\t\t\t\tposition = start\n\t\t\t\treturn\n\t\t\t}\n\t\t\tbytes[i] = byte\n\t\t}\n\t\treturn fromCharCode.apply(String, bytes)\n}\nfunction shortStringInJS(length) {\n\tif (length < 4) {\n\t\tif (length < 2) {\n\t\t\tif (length === 0)\n\t\t\t\treturn ''\n\t\t\telse {\n\t\t\t\tlet a = src[position++]\n\t\t\t\tif ((a & 0x80) > 1) {\n\t\t\t\t\tposition -= 1\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a)\n\t\t\t}\n\t\t} else {\n\t\t\tlet a = src[position++]\n\t\t\tlet b = src[position++]\n\t\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0) {\n\t\t\t\tposition -= 2\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 3)\n\t\t\t\treturn fromCharCode(a, b)\n\t\t\tlet c = src[position++]\n\t\t\tif ((c & 0x80) > 0) {\n\t\t\t\tposition -= 3\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c)\n\t\t}\n\t} else {\n\t\tlet a = src[position++]\n\t\tlet b = src[position++]\n\t\tlet c = src[position++]\n\t\tlet d = src[position++]\n\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0 || (c & 0x80) > 0 || (d & 0x80) > 0) {\n\t\t\tposition -= 4\n\t\t\treturn\n\t\t}\n\t\tif (length < 6) {\n\t\t\tif (length === 4)\n\t\t\t\treturn fromCharCode(a, b, c, d)\n\t\t\telse {\n\t\t\t\tlet e = src[position++]\n\t\t\t\tif ((e & 0x80) > 0) {\n\t\t\t\t\tposition -= 5\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e)\n\t\t\t}\n\t\t} else if (length < 8) {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0) {\n\t\t\t\tposition -= 6\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 7)\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f)\n\t\t\tlet g = src[position++]\n\t\t\tif ((g & 0x80) > 0) {\n\t\t\t\tposition -= 7\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c, d, e, f, g)\n\t\t} else {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tlet g = src[position++]\n\t\t\tlet h = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0 || (g & 0x80) > 0 || (h & 0x80) > 0) {\n\t\t\t\tposition -= 8\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 10) {\n\t\t\t\tif (length === 8)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h)\n\t\t\t\telse {\n\t\t\t\t\tlet i = src[position++]\n\t\t\t\t\tif ((i & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 9\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i)\n\t\t\t\t}\n\t\t\t} else if (length < 12) {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0) {\n\t\t\t\t\tposition -= 10\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 11)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j)\n\t\t\t\tlet k = src[position++]\n\t\t\t\tif ((k & 0x80) > 0) {\n\t\t\t\t\tposition -= 11\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k)\n\t\t\t} else {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tlet k = src[position++]\n\t\t\t\tlet l = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0 || (k & 0x80) > 0 || (l & 0x80) > 0) {\n\t\t\t\t\tposition -= 12\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 14) {\n\t\t\t\t\tif (length === 12)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l)\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\t\tif ((m & 0x80) > 0) {\n\t\t\t\t\t\t\tposition -= 13\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\tlet n = src[position++]\n\t\t\t\t\tif ((m & 0x80) > 0 || (n & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 14\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (length < 15)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n)\n\t\t\t\t\tlet o = src[position++]\n\t\t\t\t\tif ((o & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 15\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction readOnlyJSString() {\n\tlet token = src[position++]\n\tlet length\n\tif (token < 0xc0) {\n\t\t// fixstr\n\t\tlength = token - 0xa0\n\t} else {\n\t\tswitch(token) {\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tlength = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tlength = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tlength = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tthrow new Error('Expected string')\n\t\t}\n\t}\n\treturn readStringJS(length)\n}\n\n\nfunction readBin(length) {\n\treturn currentUnpackr.copyBuffers ?\n\t\t// specifically use the copying slice (not the node one)\n\t\tUint8Array.prototype.slice.call(src, position, position += length) :\n\t\tsrc.subarray(position, position += length)\n}\nfunction readExt(length) {\n\tlet type = src[position++]\n\tif (currentExtensions[type]) {\n\t\tlet end\n\t\treturn currentExtensions[type](src.subarray(position, end = (position += length)), (readPosition) => {\n\t\t\tposition = readPosition;\n\t\t\ttry {\n\t\t\t\treturn read();\n\t\t\t} finally {\n\t\t\t\tposition = end;\n\t\t\t}\n\t\t})\n\t}\n\telse\n\t\tthrow new Error('Unknown extension type ' + type)\n}\n\nvar keyCache = new Array(4096)\nfunction readKey() {\n\tlet length = src[position++]\n\tif (length >= 0xa0 && length < 0xc0) {\n\t\t// fixstr, potentially use key cache\n\t\tlength = length - 0xa0\n\t\tif (srcStringEnd >= position) // if it has been extracted, must use it (and faster anyway)\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\telse if (!(srcStringEnd == 0 && srcEnd < 180))\n\t\t\treturn readFixedString(length)\n\t} else { // not cacheable, go back and do a standard read\n\t\tposition--\n\t\treturn asSafeString(read())\n\t}\n\tlet key = ((length << 5) ^ (length > 1 ? dataView.getUint16(position) : length > 0 ? src[position] : 0)) & 0xfff\n\tlet entry = keyCache[key]\n\tlet checkPosition = position\n\tlet end = position + length - 3\n\tlet chunk\n\tlet i = 0\n\tif (entry && entry.bytes == length) {\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = dataView.getUint32(checkPosition)\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tcheckPosition += 4\n\t\t}\n\t\tend += 3\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = src[checkPosition++]\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\tif (checkPosition === end) {\n\t\t\tposition = checkPosition\n\t\t\treturn entry.string\n\t\t}\n\t\tend -= 3\n\t\tcheckPosition = position\n\t}\n\tentry = []\n\tkeyCache[key] = entry\n\tentry.bytes = length\n\twhile (checkPosition < end) {\n\t\tchunk = dataView.getUint32(checkPosition)\n\t\tentry.push(chunk)\n\t\tcheckPosition += 4\n\t}\n\tend += 3\n\twhile (checkPosition < end) {\n\t\tchunk = src[checkPosition++]\n\t\tentry.push(chunk)\n\t}\n\t// for small blocks, avoiding the overhead of the extract call is helpful\n\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\tif (string != null)\n\t\treturn entry.string = string\n\treturn entry.string = readFixedString(length)\n}\n\nfunction asSafeString(property) {\n\t// protect against expensive (DoS) string conversions\n\tif (typeof property === 'string') return property;\n\tif (typeof property === 'number' || typeof property === 'boolean' || typeof property === 'bigint') return property.toString();\n\tif (property == null) return property + '';\n\tif (currentUnpackr.allowArraysInMapKeys && Array.isArray(property) && property.flat().every(item => ['string', 'number', 'boolean', 'bigint'].includes(typeof item))) {\n\t\treturn property.flat().toString();\n\t}\n\tthrow new Error(`Invalid property type for record: ${typeof property}`);\n}\n// the registration of the record definition extension (as \"r\")\nconst recordDefinition = (id, highByte) => {\n\tlet structure = read().map(asSafeString) // ensure that all keys are strings and\n\t// that the array is mutable\n\tlet firstByte = id\n\tif (highByte !== undefined) {\n\t\tid = id < 32 ? -((highByte << 5) + id) : ((highByte << 5) + id)\n\t\tstructure.highByte = highByte\n\t}\n\tlet existingStructure = currentStructures[id]\n\t// If it is a shared structure, we need to restore any changes after reading.\n\t// Also in sequential mode, we may get incomplete reads and thus errors, and we need to restore\n\t// to the state prior to an incomplete read in order to properly resume.\n\tif (existingStructure && (existingStructure.isShared || sequentialMode)) {\n\t\t(currentStructures.restoreStructures || (currentStructures.restoreStructures = []))[id] = existingStructure\n\t}\n\tcurrentStructures[id] = structure\n\tstructure.read = createStructureReader(structure, firstByte)\n\treturn structure.read()\n}\ncurrentExtensions[0] = () => {} // notepack defines extension 0 to mean undefined, so use that as the default here\ncurrentExtensions[0].noBuffer = true\n\ncurrentExtensions[0x42] = (data) => {\n\t// decode bigint\n\tlet length = data.length;\n\tlet value = BigInt(data[0] & 0x80 ? data[0] - 0x100 : data[0]);\n\tfor (let i = 1; i < length; i++) {\n\t\tvalue <<= BigInt(8);\n\t\tvalue += BigInt(data[i]);\n\t}\n\treturn value;\n}\n\nlet errors = { Error, TypeError, ReferenceError };\ncurrentExtensions[0x65] = () => {\n\tlet data = read()\n\treturn (errors[data[0]] || Error)(data[1], { cause: data[2] })\n}\n\ncurrentExtensions[0x69] = (data) => {\n\t// id extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tif (!referenceMap)\n\t\treferenceMap = new Map()\n\tlet token = src[position]\n\tlet target\n\t// TODO: handle any other types that can cycle and make the code more robust if there are other extensions\n\tif (token >= 0x90 && token < 0xa0 || token == 0xdc || token == 0xdd)\n\t\ttarget = []\n\telse if (token >= 0x80 && token < 0x90 || token == 0xde || token == 0xdf)\n\t\ttarget = new Map()\n\telse if ((token >= 0xc7 && token <= 0xc9 || token >= 0xd4 && token <= 0xd8) && src[position + 1] === 0x73)\n\t\ttarget = new Set()\n\telse\n\t\ttarget = {}\n\n\tlet refEntry = { target } // a placeholder object\n\treferenceMap.set(id, refEntry)\n\tlet targetProperties = read() // read the next value as the target object to id\n\tif (!refEntry.used) {\n\t\t// no cycle, can just use the returned read object\n\t\treturn refEntry.target = targetProperties // replace the placeholder with the real one\n\t} else {\n\t\t// there is a cycle, so we have to assign properties to original target\n\t\tObject.assign(target, targetProperties)\n\t}\n\n\t// copy over map/set entries if we're able to\n\tif (target instanceof Map)\n\t\tfor (let [k, v] of targetProperties.entries()) target.set(k, v)\n\tif (target instanceof Set)\n\t\tfor (let i of Array.from(targetProperties)) target.add(i)\n\treturn target\n}\n\ncurrentExtensions[0x70] = (data) => {\n\t// pointer extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tlet refEntry = referenceMap.get(id)\n\trefEntry.used = true\n\treturn refEntry.target\n}\n\ncurrentExtensions[0x73] = () => new Set(read())\n\nexport const typedArrays = ['Int8','Uint8','Uint8Clamped','Int16','Uint16','Int32','Uint32','Float32','Float64','BigInt64','BigUint64'].map(type => type + 'Array')\n\nlet glbl = typeof globalThis === 'object' ? globalThis : window;\ncurrentExtensions[0x74] = (data) => {\n\tlet typeCode = data[0]\n\t// we always have to slice to get a new ArrayBuffer that is aligned\n\tlet buffer = Uint8Array.prototype.slice.call(data, 1).buffer\n\n\tlet typedArrayName = typedArrays[typeCode]\n\tif (!typedArrayName) {\n\t\tif (typeCode === 16) return buffer\n\t\tif (typeCode === 17) return new DataView(buffer)\n\t\tthrow new Error('Could not find typed array for code ' + typeCode)\n\t}\n\treturn new glbl[typedArrayName](buffer)\n}\ncurrentExtensions[0x78] = () => {\n\tlet data = read()\n\treturn new RegExp(data[0], data[1])\n}\nconst TEMP_BUNDLE = []\ncurrentExtensions[0x62] = (data) => {\n\tlet dataSize = (data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]\n\tlet dataPosition = position\n\tposition += dataSize - data.length\n\tbundledStrings = TEMP_BUNDLE\n\tbundledStrings = [readOnlyJSString(), readOnlyJSString()]\n\tbundledStrings.position0 = 0\n\tbundledStrings.position1 = 0\n\tbundledStrings.postBundlePosition = position\n\tposition = dataPosition\n\treturn read()\n}\n\ncurrentExtensions[0xff] = (data) => {\n\t// 32-bit date extension\n\tif (data.length == 4)\n\t\treturn new Date((data[0] * 0x1000000 + (data[1] << 16) + (data[2] << 8) + data[3]) * 1000)\n\telse if (data.length == 8)\n\t\treturn new Date(\n\t\t\t((data[0] << 22) + (data[1] << 14) + (data[2] << 6) + (data[3] >> 2)) / 1000000 +\n\t\t\t((data[3] & 0x3) * 0x100000000 + data[4] * 0x1000000 + (data[5] << 16) + (data[6] << 8) + data[7]) * 1000)\n\telse if (data.length == 12)\n\t\treturn new Date(\n\t\t\t((data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]) / 1000000 +\n\t\t\t(((data[4] & 0x80) ? -0x1000000000000 : 0) + data[6] * 0x10000000000 + data[7] * 0x100000000 + data[8] * 0x1000000 + (data[9] << 16) + (data[10] << 8) + data[11]) * 1000)\n\telse\n\t\treturn new Date('invalid')\n}\n// registration of bulk record definition?\n// currentExtensions[0x52] = () =>\n\nfunction saveState(callback) {\n\tif (onSaveState)\n\t\tonSaveState();\n\tlet savedSrcEnd = srcEnd\n\tlet savedPosition = position\n\tlet savedStringPosition = stringPosition\n\tlet savedSrcStringStart = srcStringStart\n\tlet savedSrcStringEnd = srcStringEnd\n\tlet savedSrcString = srcString\n\tlet savedStrings = strings\n\tlet savedReferenceMap = referenceMap\n\tlet savedBundledStrings = bundledStrings\n\n\t// TODO: We may need to revisit this if we do more external calls to user code (since it could be slow)\n\tlet savedSrc = new Uint8Array(src.slice(0, srcEnd)) // we copy the data in case it changes while external data is processed\n\tlet savedStructures = currentStructures\n\tlet savedStructuresContents = currentStructures.slice(0, currentStructures.length)\n\tlet savedPackr = currentUnpackr\n\tlet savedSequentialMode = sequentialMode\n\tlet value = callback()\n\tsrcEnd = savedSrcEnd\n\tposition = savedPosition\n\tstringPosition = savedStringPosition\n\tsrcStringStart = savedSrcStringStart\n\tsrcStringEnd = savedSrcStringEnd\n\tsrcString = savedSrcString\n\tstrings = savedStrings\n\treferenceMap = savedReferenceMap\n\tbundledStrings = savedBundledStrings\n\tsrc = savedSrc\n\tsequentialMode = savedSequentialMode\n\tcurrentStructures = savedStructures\n\tcurrentStructures.splice(0, currentStructures.length, ...savedStructuresContents)\n\tcurrentUnpackr = savedPackr\n\tdataView = new DataView(src.buffer, src.byteOffset, src.byteLength)\n\treturn value\n}\nexport function clearSource() {\n\tsrc = null\n\treferenceMap = null\n\tcurrentStructures = null\n}\n\nexport function addExtension(extension) {\n\tif (extension.unpack)\n\t\tcurrentExtensions[extension.type] = extension.unpack\n\telse\n\t\tcurrentExtensions[extension.type] = extension\n}\n\nexport const mult10 = new Array(147) // this is a table matching binary exponents to the multiplier to determine significant digit rounding\nfor (let i = 0; i < 256; i++) {\n\tmult10[i] = +('1e' + Math.floor(45.15 - i * 0.30103))\n}\nexport const Decoder = Unpackr\nvar defaultUnpackr = new Unpackr({ useRecords: false })\nexport const unpack = defaultUnpackr.unpack\nexport const unpackMultiple = defaultUnpackr.unpackMultiple\nexport const decode = defaultUnpackr.unpack\nexport const FLOAT32_OPTIONS = {\n\tNEVER: 0,\n\tALWAYS: 1,\n\tDECIMAL_ROUND: 3,\n\tDECIMAL_FIT: 4\n}\nlet f32Array = new Float32Array(1)\nlet u8Array = new Uint8Array(f32Array.buffer, 0, 4)\nexport function roundFloat32(float32Number) {\n\tf32Array[0] = float32Number\n\tlet multiplier = mult10[((u8Array[3] & 0x7f) << 1) | (u8Array[2] >> 7)]\n\treturn ((multiplier * float32Number + (float32Number > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n}\nexport function setReadStruct(updatedReadStruct, loadedStructs, saveState) {\n\treadStruct = updatedReadStruct;\n\tonLoadedStructures = loadedStructs;\n\tonSaveState = saveState;\n}\n", "import { Unpackr, mult10, C1Type, typedArrays, addExtension as unpackAddExtension } from './unpack.js'\nlet textEncoder\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nlet extensions, extensionClasses\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nconst ByteArrayAllocate = hasNodeBuffer ?\n\tfunction(length) { return Buffer.allocUnsafeSlow(length) } : Uint8Array\nconst ByteArray = hasNodeBuffer ? Buffer : Uint8Array\nconst MAX_BUFFER_SIZE = hasNodeBuffer ? 0x100000000 : 0x7fd00000\nlet target, keysTarget\nlet targetView\nlet position = 0\nlet safeEnd\nlet bundledStrings = null\nlet writeStructSlots\nconst MAX_BUNDLE_SIZE = 0x5500 // maximum characters such that the encoded bytes fits in 16 bits.\nconst hasNonLatin = /[\\u0080-\\uFFFF]/\nexport const RECORD_SYMBOL = Symbol('record-id')\nexport class Packr extends Unpackr {\n\tconstructor(options) {\n\t\tsuper(options)\n\t\tthis.offset = 0\n\t\tlet typeBuffer\n\t\tlet start\n\t\tlet hasSharedUpdate\n\t\tlet structures\n\t\tlet referenceMap\n\t\tlet encodeUtf8 = ByteArray.prototype.utf8Write ? function(string, position) {\n\t\t\treturn target.utf8Write(string, position, target.byteLength - position)\n\t\t} : (textEncoder && textEncoder.encodeInto) ?\n\t\t\tfunction(string, position) {\n\t\t\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t\t\t} : false\n\n\t\tlet packr = this\n\t\tif (!options)\n\t\t\toptions = {}\n\t\tlet isSequential = options && options.sequential\n\t\tlet hasSharedStructures = options.structures || options.saveStructures\n\t\tlet maxSharedStructures = options.maxSharedStructures\n\t\tif (maxSharedStructures == null)\n\t\t\tmaxSharedStructures = hasSharedStructures ? 32 : 0\n\t\tif (maxSharedStructures > 8160)\n\t\t\tthrow new Error('Maximum maxSharedStructure is 8160')\n\t\tif (options.structuredClone && options.moreTypes == undefined) {\n\t\t\tthis.moreTypes = true\n\t\t}\n\t\tlet maxOwnStructures = options.maxOwnStructures\n\t\tif (maxOwnStructures == null)\n\t\t\tmaxOwnStructures = hasSharedStructures ? 32 : 64\n\t\tif (!this.structures && options.useRecords != false)\n\t\t\tthis.structures = []\n\t\t// two byte record ids for shared structures\n\t\tlet useTwoByteRecords = maxSharedStructures > 32 || (maxOwnStructures + maxSharedStructures > 64)\n\t\tlet sharedLimitId = maxSharedStructures + 0x40\n\t\tlet maxStructureId = maxSharedStructures + maxOwnStructures + 0x40\n\t\tif (maxStructureId > 8256) {\n\t\t\tthrow new Error('Maximum maxSharedStructure + maxOwnStructure is 8192')\n\t\t}\n\t\tlet recordIdsToRemove = []\n\t\tlet transitionsCount = 0\n\t\tlet serializationsSinceTransitionRebuild = 0\n\n\t\tthis.pack = this.encode = function(value, encodeOptions) {\n\t\t\tif (!target) {\n\t\t\t\ttarget = new ByteArrayAllocate(8192)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, 8192))\n\t\t\t\tposition = 0\n\t\t\t}\n\t\t\tsafeEnd = target.length - 10\n\t\t\tif (safeEnd - position < 0x800) {\n\t\t\t\t// don't start too close to the end,\n\t\t\t\ttarget = new ByteArrayAllocate(target.length)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, target.length))\n\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\tposition = 0\n\t\t\t} else\n\t\t\t\tposition = (position + 7) & 0x7ffffff8 // Word align to make any future copying of this buffer faster\n\t\t\tstart = position\n\t\t\tif (encodeOptions & RESERVE_START_SPACE) position += (encodeOptions & 0xff)\n\t\t\treferenceMap = packr.structuredClone ? new Map() : null\n\t\t\tif (packr.bundleStrings && typeof value !== 'string') {\n\t\t\t\tbundledStrings = []\n\t\t\t\tbundledStrings.size = Infinity // force a new bundle start on first string\n\t\t\t} else\n\t\t\t\tbundledStrings = null\n\t\t\tstructures = packr.structures\n\t\t\tif (structures) {\n\t\t\t\tif (structures.uninitialized)\n\t\t\t\t\tstructures = packr._mergeStructures(packr.getStructures())\n\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\tif (sharedLength > maxSharedStructures) {\n\t\t\t\t\t//if (maxSharedStructures <= 32 && structures.sharedLength > 32) // TODO: could support this, but would need to update the limit ids\n\t\t\t\t\tthrow new Error('Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to ' + structures.sharedLength)\n\t\t\t\t}\n\t\t\t\tif (!structures.transitions) {\n\t\t\t\t\t// rebuild our structure transitions\n\t\t\t\t\tstructures.transitions = Object.create(null)\n\t\t\t\t\tfor (let i = 0; i < sharedLength; i++) {\n\t\t\t\t\t\tlet keys = structures[i]\n\t\t\t\t\t\tif (!keys)\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tlet nextTransition, transition = structures.transitions\n\t\t\t\t\t\tfor (let j = 0, l = keys.length; j < l; j++) {\n\t\t\t\t\t\t\tlet key = keys[j]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i + 0x40\n\t\t\t\t\t}\n\t\t\t\t\tthis.lastNamedStructuresLength = sharedLength\n\t\t\t\t}\n\t\t\t\tif (!isSequential) {\n\t\t\t\t\tstructures.nextId = sharedLength + 0x40\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (hasSharedUpdate)\n\t\t\t\thasSharedUpdate = false\n\t\t\tlet encodingError;\n\t\t\ttry {\n\t\t\t\tif (packr.randomAccessStructure && value && value.constructor && value.constructor === Object)\n\t\t\t\t\twriteStruct(value);\n\t\t\t\telse\n\t\t\t\t\tpack(value)\n\t\t\t\tlet lastBundle = bundledStrings;\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\twriteBundles(start, pack, 0)\n\t\t\t\tif (referenceMap && referenceMap.idsToInsert) {\n\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert.sort((a, b) => a.offset > b.offset ? 1 : -1);\n\t\t\t\t\tlet i = idsToInsert.length;\n\t\t\t\t\tlet incrementPosition = -1;\n\t\t\t\t\twhile (lastBundle && i > 0) {\n\t\t\t\t\t\tlet insertionPoint = idsToInsert[--i].offset + start;\n\t\t\t\t\t\tif (insertionPoint < (lastBundle.stringsPosition + start) && incrementPosition === -1)\n\t\t\t\t\t\t\tincrementPosition = 0;\n\t\t\t\t\t\tif (insertionPoint > (lastBundle.position + start)) {\n\t\t\t\t\t\t\tif (incrementPosition >= 0)\n\t\t\t\t\t\t\t\tincrementPosition += 6;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (incrementPosition >= 0) {\n\t\t\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t\t\t\tincrementPosition = -1; // reset\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlastBundle = lastBundle.previous;\n\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (incrementPosition >= 0 && lastBundle) {\n\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t}\n\t\t\t\t\tposition += idsToInsert.length * 6;\n\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\tpackr.offset = position\n\t\t\t\t\tlet serialized = insertIds(target.subarray(start, position), idsToInsert)\n\t\t\t\t\treferenceMap = null\n\t\t\t\t\treturn serialized\n\t\t\t\t}\n\t\t\t\tpackr.offset = position // update the offset so next serialization doesn't write over our buffer, but can continue writing to same buffer sequentially\n\t\t\t\tif (encodeOptions & REUSE_BUFFER_MODE) {\n\t\t\t\t\ttarget.start = start\n\t\t\t\t\ttarget.end = position\n\t\t\t\t\treturn target\n\t\t\t\t}\n\t\t\t\treturn target.subarray(start, position) // position can change if we call pack again in saveStructures, so we get the buffer now\n\t\t\t} catch(error) {\n\t\t\t\tencodingError = error;\n\t\t\t\tthrow error;\n\t\t\t} finally {\n\t\t\t\tif (structures) {\n\t\t\t\t\tresetStructures();\n\t\t\t\t\tif (hasSharedUpdate && packr.saveStructures) {\n\t\t\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\t\t\t// we can't rely on start/end with REUSE_BUFFER_MODE since they will (probably) change when we save\n\t\t\t\t\t\tlet returnBuffer = target.subarray(start, position)\n\t\t\t\t\t\tlet newSharedData = prepareStructures(structures, packr);\n\t\t\t\t\t\tif (!encodingError) { // TODO: If there is an encoding error, should make the structures as uninitialized so they get rebuilt next time\n\t\t\t\t\t\t\tif (packr.saveStructures(newSharedData, newSharedData.isCompatible) === false) {\n\t\t\t\t\t\t\t\t// get updated structures and try again if the update failed\n\t\t\t\t\t\t\t\treturn packr.pack(value, encodeOptions)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpackr.lastNamedStructuresLength = sharedLength\n\t\t\t\t\t\t\t// don't keep large buffers around\n\t\t\t\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\t\t\t\treturn returnBuffer\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// don't keep large buffers around, they take too much memory and cause problems (limit at 1GB)\n\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\tif (encodeOptions & RESET_BUFFER_MODE)\n\t\t\t\t\tposition = start\n\t\t\t}\n\t\t}\n\t\tconst resetStructures = () => {\n\t\t\tif (serializationsSinceTransitionRebuild < 10)\n\t\t\t\tserializationsSinceTransitionRebuild++\n\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\tif (structures.length > sharedLength && !isSequential)\n\t\t\t\tstructures.length = sharedLength\n\t\t\tif (transitionsCount > 10000) {\n\t\t\t\t// force a rebuild occasionally after a lot of transitions so it can get cleaned up\n\t\t\t\tstructures.transitions = null\n\t\t\t\tserializationsSinceTransitionRebuild = 0\n\t\t\t\ttransitionsCount = 0\n\t\t\t\tif (recordIdsToRemove.length > 0)\n\t\t\t\t\trecordIdsToRemove = []\n\t\t\t} else if (recordIdsToRemove.length > 0 && !isSequential) {\n\t\t\t\tfor (let i = 0, l = recordIdsToRemove.length; i < l; i++) {\n\t\t\t\t\trecordIdsToRemove[i][RECORD_SYMBOL] = 0\n\t\t\t\t}\n\t\t\t\trecordIdsToRemove = []\n\t\t\t}\n\t\t}\n\t\tconst packArray = (value) => {\n\t\t\tvar length = value.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x90 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xdc\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdd\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tpack(value[i])\n\t\t\t}\n\t\t}\n\t\tconst pack = (value) => {\n\t\t\tif (position > safeEnd)\n\t\t\t\ttarget = makeRoom(position)\n\n\t\t\tvar type = typeof value\n\t\t\tvar length\n\t\t\tif (type === 'string') {\n\t\t\t\tlet strLength = value.length\n\t\t\t\tif (bundledStrings && strLength >= 4 && strLength < 0x1000) {\n\t\t\t\t\tif ((bundledStrings.size += strLength) > MAX_BUNDLE_SIZE) {\n\t\t\t\t\t\tlet extStart\n\t\t\t\t\t\tlet maxBytes = (bundledStrings[0] ? bundledStrings[0].length * 3 + bundledStrings[1].length : 0) + 10\n\t\t\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\t\t\t\t\t\tlet lastBundle\n\t\t\t\t\t\tif (bundledStrings.position) { // here we use the 0x62 extension to write the last bundle and reserve space for the reference pointer to the next/current bundle\n\t\t\t\t\t\t\tlastBundle = bundledStrings\n\t\t\t\t\t\t\ttarget[position] = 0xc8 // ext 16\n\t\t\t\t\t\t\tposition += 3 // reserve for the writing bundle size\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t\twriteBundles(start, pack, 0) // write the last bundles\n\t\t\t\t\t\t\ttargetView.setUint16(extStart + start - 3, position - start - extStart)\n\t\t\t\t\t\t} else { // here we use the 0x62 extension just to reserve the space for the reference pointer to the bundle (will be updated once the bundle is written)\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbundledStrings = ['', ''] // create new ones\n\t\t\t\t\t\tbundledStrings.previous = lastBundle;\n\t\t\t\t\t\tbundledStrings.size = 0\n\t\t\t\t\t\tbundledStrings.position = extStart\n\t\t\t\t\t}\n\t\t\t\t\tlet twoByte = hasNonLatin.test(value)\n\t\t\t\t\tbundledStrings[twoByte ? 0 : 1] += value\n\t\t\t\t\ttarget[position++] = 0xc1\n\t\t\t\t\tpack(twoByte ? -strLength : strLength);\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet headerSize\n\t\t\t\t// first we estimate the header size, so we can write to the correct location\n\t\t\t\tif (strLength < 0x20) {\n\t\t\t\t\theaderSize = 1\n\t\t\t\t} else if (strLength < 0x100) {\n\t\t\t\t\theaderSize = 2\n\t\t\t\t} else if (strLength < 0x10000) {\n\t\t\t\t\theaderSize = 3\n\t\t\t\t} else {\n\t\t\t\t\theaderSize = 5\n\t\t\t\t}\n\t\t\t\tlet maxBytes = strLength * 3\n\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\n\t\t\t\tif (strLength < 0x40 || !encodeUtf8) {\n\t\t\t\t\tlet i, c1, c2, strPosition = position + headerSize\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlength = strPosition - position - headerSize\n\t\t\t\t} else {\n\t\t\t\t\tlength = encodeUtf8(value, position + headerSize)\n\t\t\t\t}\n\n\t\t\t\tif (length < 0x20) {\n\t\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\tif (headerSize < 2) {\n\t\t\t\t\t\ttarget.copyWithin(position + 2, position + 1, position + 1 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\ttarget[position++] = length\n\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\tif (headerSize < 3) {\n\t\t\t\t\t\ttarget.copyWithin(position + 3, position + 2, position + 2 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xda\n\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t} else {\n\t\t\t\t\tif (headerSize < 5) {\n\t\t\t\t\t\ttarget.copyWithin(position + 5, position + 3, position + 3 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xdb\n\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\tposition += 4\n\t\t\t\t}\n\t\t\t\tposition += length\n\t\t\t} else if (type === 'number') {\n\t\t\t\tif (value >>> 0 === value) {// positive integer, 32-bit or less\n\t\t\t\t\t// positive uint\n\t\t\t\t\tif (value < 0x20 || (value < 0x80 && this.useRecords === false) || (value < 0x40 && !this.randomAccessStructure)) {\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x100) {\n\t\t\t\t\t\ttarget[position++] = 0xcc\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0xcd\n\t\t\t\t\t\ttarget[position++] = value >> 8\n\t\t\t\t\t\ttarget[position++] = value & 0xff\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xce\n\t\t\t\t\t\ttargetView.setUint32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else if (value >> 0 === value) { // negative integer\n\t\t\t\t\tif (value >= -0x20) {\n\t\t\t\t\t\ttarget[position++] = 0x100 + value\n\t\t\t\t\t} else if (value >= -0x80) {\n\t\t\t\t\t\ttarget[position++] = 0xd0\n\t\t\t\t\t\ttarget[position++] = value + 0x100\n\t\t\t\t\t} else if (value >= -0x8000) {\n\t\t\t\t\t\ttarget[position++] = 0xd1\n\t\t\t\t\t\ttargetView.setInt16(position, value)\n\t\t\t\t\t\tposition += 2\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xd2\n\t\t\t\t\t\ttargetView.setInt32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet useFloat32\n\t\t\t\t\tif ((useFloat32 = this.useFloat32) > 0 && value < 0x100000000 && value >= -0x80000000) {\n\t\t\t\t\t\ttarget[position++] = 0xca\n\t\t\t\t\t\ttargetView.setFloat32(position, value)\n\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\tif (useFloat32 < 4 ||\n\t\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\t((xShifted = value * mult10[((target[position] & 0x7f) << 1) | (target[position + 1] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tposition-- // move back into position for writing a double\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\ttargetView.setFloat64(position, value)\n\t\t\t\t\tposition += 8\n\t\t\t\t}\n\t\t\t} else if (type === 'object' || type === 'function') {\n\t\t\t\tif (!value)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\tif (referenceMap) {\n\t\t\t\t\t\tlet referee = referenceMap.get(value)\n\t\t\t\t\t\tif (referee) {\n\t\t\t\t\t\t\tif (!referee.id) {\n\t\t\t\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert || (referenceMap.idsToInsert = [])\n\t\t\t\t\t\t\t\treferee.id = idsToInsert.push(referee)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x70 // \"p\" for pointer\n\t\t\t\t\t\t\ttargetView.setUint32(position, referee.id)\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treferenceMap.set(value, { offset: position - start })\n\t\t\t\t\t}\n\t\t\t\t\tlet constructor = value.constructor\n\t\t\t\t\tif (constructor === Object) {\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t} else if (constructor === Array) {\n\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t} else if (constructor === Map) {\n\t\t\t\t\t\tif (this.mapAsEmptyObject) target[position++] = 0x80\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tlength = value.size\n\t\t\t\t\t\t\tif (length < 0x10) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xde\n\t\t\t\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor (let [key, entryValue] of value) {\n\t\t\t\t\t\t\t\tpack(key)\n\t\t\t\t\t\t\t\tpack(entryValue)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfor (let i = 0, l = extensions.length; i < l; i++) {\n\t\t\t\t\t\t\tlet extensionClass = extensionClasses[i]\n\t\t\t\t\t\t\tif (value instanceof extensionClass) {\n\t\t\t\t\t\t\t\tlet extension = extensions[i]\n\t\t\t\t\t\t\t\tif (extension.write) {\n\t\t\t\t\t\t\t\t\tif (extension.type) {\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd4 // one byte \"tag\" extension\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = extension.type\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet writeResult = extension.write.call(this, value)\n\t\t\t\t\t\t\t\t\tif (writeResult === value) { // avoid infinite recursion\n\t\t\t\t\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tpack(writeResult)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet currentTarget = target\n\t\t\t\t\t\t\t\tlet currentTargetView = targetView\n\t\t\t\t\t\t\t\tlet currentPosition = position\n\t\t\t\t\t\t\t\ttarget = null\n\t\t\t\t\t\t\t\tlet result\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tresult = extension.pack.call(this, value, (size) => {\n\t\t\t\t\t\t\t\t\t\t// restore target and use it\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\tcurrentTarget = null\n\t\t\t\t\t\t\t\t\t\tposition += size\n\t\t\t\t\t\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\ttarget, targetView, position: position - size\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}, pack)\n\t\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\t\t// restore current target information (unless already restored)\n\t\t\t\t\t\t\t\t\tif (currentTarget) {\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\ttargetView = currentTargetView\n\t\t\t\t\t\t\t\t\t\tposition = currentPosition\n\t\t\t\t\t\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (result) {\n\t\t\t\t\t\t\t\t\tif (result.length + position > safeEnd)\n\t\t\t\t\t\t\t\t\t\tmakeRoom(result.length + position)\n\t\t\t\t\t\t\t\t\tposition = writeExtensionData(result, target, position, extension.type)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// check isArray after extensions, because extensions can extend Array\n\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// use this as an alternate mechanism for expressing how to serialize\n\t\t\t\t\t\t\tif (value.toJSON) {\n\t\t\t\t\t\t\t\tconst json = value.toJSON()\n\t\t\t\t\t\t\t\t// if for some reason value.toJSON returns itself it'll loop forever\n\t\t\t\t\t\t\t\tif (json !== value)\n\t\t\t\t\t\t\t\t\treturn pack(json)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// if there is a writeFunction, use it, otherwise just encode as undefined\n\t\t\t\t\t\t\tif (type === 'function')\n\t\t\t\t\t\t\t\treturn pack(this.writeFunction && this.writeFunction(value));\n\n\t\t\t\t\t\t\t// no extension found, write as plain object\n\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (type === 'boolean') {\n\t\t\t\ttarget[position++] = value ? 0xc3 : 0xc2\n\t\t\t} else if (type === 'bigint') {\n\t\t\t\tif (value < 0x8000000000000000 && value >= -0x8000000000000000) {\n\t\t\t\t\t// use a signed int as long as it fits\n\t\t\t\t\ttarget[position++] = 0xd3\n\t\t\t\t\ttargetView.setBigInt64(position, value)\n\t\t\t\t} else if (value < 0x10000000000000000 && value > 0) {\n\t\t\t\t\t// if we can fit an unsigned int, use that\n\t\t\t\t\ttarget[position++] = 0xcf\n\t\t\t\t\ttargetView.setBigUint64(position, value)\n\t\t\t\t} else {\n\t\t\t\t\t// overflow\n\t\t\t\t\tif (this.largeBigIntToFloat) {\n\t\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\t\ttargetView.setFloat64(position, Number(value))\n\t\t\t\t\t} else if (this.largeBigIntToString) {\n\t\t\t\t\t\treturn pack(value.toString());\n\t\t\t\t\t} else if ((this.useBigIntExtension || this.moreTypes) && value < BigInt(2)**BigInt(1023) && value > -(BigInt(2)**BigInt(1023))) {\n\t\t\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\t\t\tposition++;\n\t\t\t\t\t\ttarget[position++] = 0x42 // \"B\" for BigInt\n\t\t\t\t\t\tlet bytes = [];\n\t\t\t\t\t\tlet alignedSign;\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tlet byte = value & BigInt(0xff);\n\t\t\t\t\t\t\talignedSign = (byte & BigInt(0x80)) === (value < BigInt(0) ? BigInt(0x80) : BigInt(0));\n\t\t\t\t\t\t\tbytes.push(byte);\n\t\t\t\t\t\t\tvalue >>= BigInt(8);\n\t\t\t\t\t\t} while (!((value === BigInt(0) || value === BigInt(-1)) && alignedSign));\n\t\t\t\t\t\ttarget[position-2] = bytes.length;\n\t\t\t\t\t\tfor (let i = bytes.length; i > 0;) {\n\t\t\t\t\t\t\ttarget[position++] = Number(bytes[--i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new RangeError(value + ' was too large to fit in MessagePack 64-bit integer format, use' +\n\t\t\t\t\t\t\t' useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set' +\n\t\t\t\t\t\t\t' largeBigIntToString to convert to string')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tposition += 8\n\t\t\t} else if (type === 'undefined') {\n\t\t\t\tif (this.encodeUndefinedAsNil)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\ttarget[position++] = 0xd4 // a number of implementations use fixext1 with type 0, data 0 to denote undefined, so we follow suite\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error('Unknown type: ' + type)\n\t\t\t}\n\t\t}\n\n\t\tconst writePlainObject = (this.variableMapSize || this.coercibleKeyAsNumber || this.skipValues) ? (object) => {\n\t\t\t// this method is slightly slower, but generates \"preferred serialization\" (optimally small for smaller objects)\n\t\t\tlet keys;\n\t\t\tif (this.skipValues) {\n\t\t\t\tkeys = [];\n\t\t\t\tfor (let key in object) {\n\t\t\t\t\tif ((typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) &&\n\t\t\t\t\t\t!this.skipValues.includes(object[key]))\n\t\t\t\t\t\tkeys.push(key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tkeys = Object.keys(object)\n\t\t\t}\n\t\t\tlet length = keys.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xde\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tlet key\n\t\t\tif (this.coercibleKeyAsNumber) {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tkey = keys[i]\n\t\t\t\t\tlet num = Number(key)\n\t\t\t\t\tpack(isNaN(num) ? key : num)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tpack(key = keys[i])\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\ttarget[position++] = 0xde // always using map 16, so we can preallocate and set the length afterwards\n\t\t\tlet objectOffset = position - start\n\t\t\tposition += 2\n\t\t\tlet size = 0\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(key)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (size > 0xffff) {\n\t\t\t\tthrow new Error('Object is too large to serialize with fast 16-bit map size,' +\n\t\t\t\t' use the \"variableMapSize\" option to serialize this object');\n\t\t\t}\n\t\t\ttarget[objectOffset++ + start] = size >> 8\n\t\t\ttarget[objectOffset + start] = size & 0xff\n\t\t}\n\n\t\tconst writeRecord = this.useRecords === false ? writePlainObject :\n\t\t(options.progressiveRecords && !useTwoByteRecords) ?  // this is about 2% faster for highly stable structures, since it only requires one for-in loop (but much more expensive when new structure needs to be written)\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet objectOffset = position++ - start\n\t\t\tlet wroteKeys\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (nextTransition)\n\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\telse {\n\t\t\t\t\t\t// record doesn't exist, create full new record and insert it\n\t\t\t\t\t\tlet keys = Object.keys(object)\n\t\t\t\t\t\tlet lastTransition = transition\n\t\t\t\t\t\ttransition = structures.transitions\n\t\t\t\t\t\tlet newTransitions = 0\n\t\t\t\t\t\tfor (let i = 0, l = keys.length; i < l; i++) {\n\t\t\t\t\t\t\tlet key = keys[i]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (objectOffset + start + 1 == position) {\n\t\t\t\t\t\t\t// first key, so we don't need to insert, we can just write record directly\n\t\t\t\t\t\t\tposition--\n\t\t\t\t\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\t\t\t\t} else // otherwise we need to insert the record, moving existing data after the record\n\t\t\t\t\t\t\tinsertNewRecord(transition, keys, objectOffset, newTransitions)\n\t\t\t\t\t\twroteKeys = true\n\t\t\t\t\t\ttransition = lastTransition[key]\n\t\t\t\t\t}\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!wroteKeys) {\n\t\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\t\tif (recordId)\n\t\t\t\t\ttarget[objectOffset + start] = recordId\n\t\t\t\telse\n\t\t\t\t\tinsertNewRecord(transition, Object.keys(object), objectOffset, 0)\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet newTransitions = 0\n\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\tnextTransition = transition[key]\n\t\t\t\tif (!nextTransition) {\n\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\tnewTransitions++\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition\n\t\t\t}\n\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\tif (recordId) {\n\t\t\t\tif (recordId >= 0x60 && useTwoByteRecords) {\n\t\t\t\t\ttarget[position++] = ((recordId -= 0x60) & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = recordId >> 5\n\t\t\t\t} else\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t} else {\n\t\t\t\tnewRecord(transition, transition.__keys__ || Object.keys(object), newTransitions)\n\t\t\t}\n\t\t\t// now write the values\n\t\t\tfor (let key in object)\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t}\n\n\t\t// create reference to useRecords if useRecords is a function\n\t\tconst checkUseRecords = typeof this.useRecords == 'function' && this.useRecords;\n\n\t\tconst writeObject = checkUseRecords ? (object) => {\n\t\t\tcheckUseRecords(object) ? writeRecord(object) : writePlainObject(object)\n\t\t} : writeRecord\n\n\t\tconst makeRoom = (end) => {\n\t\t\tlet newSize\n\t\t\tif (end > 0x1000000) {\n\t\t\t\t// special handling for really large buffers\n\t\t\t\tif ((end - start) > MAX_BUFFER_SIZE)\n\t\t\t\t\tthrow new Error('Packed buffer would be larger than maximum buffer size')\n\t\t\t\tnewSize = Math.min(MAX_BUFFER_SIZE,\n\t\t\t\t\tMath.round(Math.max((end - start) * (end > 0x4000000 ? 1.25 : 2), 0x400000) / 0x1000) * 0x1000)\n\t\t\t} else // faster handling for smaller buffers\n\t\t\t\tnewSize = ((Math.max((end - start) << 2, target.length - 1) >> 12) + 1) << 12\n\t\t\tlet newBuffer = new ByteArrayAllocate(newSize)\n\t\t\ttargetView = newBuffer.dataView || (newBuffer.dataView = new DataView(newBuffer.buffer, 0, newSize))\n\t\t\tend = Math.min(end, target.length)\n\t\t\tif (target.copy)\n\t\t\t\ttarget.copy(newBuffer, 0, start, end)\n\t\t\telse\n\t\t\t\tnewBuffer.set(target.slice(start, end))\n\t\t\tposition -= start\n\t\t\tstart = 0\n\t\t\tsafeEnd = newBuffer.length - 10\n\t\t\treturn target = newBuffer\n\t\t}\n\t\tconst newRecord = (transition, keys, newTransitions) => {\n\t\t\tlet recordId = structures.nextId\n\t\t\tif (!recordId)\n\t\t\t\trecordId = 0x40\n\t\t\tif (recordId < sharedLimitId && this.shouldShareStructure && !this.shouldShareStructure(keys)) {\n\t\t\t\trecordId = structures.nextOwnId\n\t\t\t\tif (!(recordId < maxStructureId))\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextOwnId = recordId + 1\n\t\t\t} else {\n\t\t\t\tif (recordId >= maxStructureId)// cycle back around\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextId = recordId + 1\n\t\t\t}\n\t\t\tlet highByte = keys.highByte = recordId >= 0x60 && useTwoByteRecords ? (recordId - 0x60) >> 5 : -1\n\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\ttransition.__keys__ = keys\n\t\t\tstructures[recordId - 0x40] = keys\n\n\t\t\tif (recordId < sharedLimitId) {\n\t\t\t\tkeys.isShared = true\n\t\t\t\tstructures.sharedLength = recordId - 0x3f\n\t\t\t\thasSharedUpdate = true\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = 0xd5 // fixext 2\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = 0xd4 // fixext 1\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\n\t\t\t\tif (newTransitions)\n\t\t\t\t\ttransitionsCount += serializationsSinceTransitionRebuild * newTransitions\n\t\t\t\t// record the removal of the id, we can maintain our shared structure\n\t\t\t\tif (recordIdsToRemove.length >= maxOwnStructures)\n\t\t\t\t\trecordIdsToRemove.shift()[RECORD_SYMBOL] = 0 // we are cycling back through, and have to remove old ones\n\t\t\t\trecordIdsToRemove.push(transition)\n\t\t\t\tpack(keys)\n\t\t\t}\n\t\t}\n\t\tconst insertNewRecord = (transition, keys, insertionOffset, newTransitions) => {\n\t\t\tlet mainTarget = target\n\t\t\tlet mainPosition = position\n\t\t\tlet mainSafeEnd = safeEnd\n\t\t\tlet mainStart = start\n\t\t\ttarget = keysTarget\n\t\t\tposition = 0\n\t\t\tstart = 0\n\t\t\tif (!target)\n\t\t\t\tkeysTarget = target = new ByteArrayAllocate(8192)\n\t\t\tsafeEnd = target.length - 10\n\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\tkeysTarget = target\n\t\t\tlet keysPosition = position\n\t\t\ttarget = mainTarget\n\t\t\tposition = mainPosition\n\t\t\tsafeEnd = mainSafeEnd\n\t\t\tstart = mainStart\n\t\t\tif (keysPosition > 1) {\n\t\t\t\tlet newEnd = position + keysPosition - 1\n\t\t\t\tif (newEnd > safeEnd)\n\t\t\t\t\tmakeRoom(newEnd)\n\t\t\t\tlet insertionPosition = insertionOffset + start\n\t\t\t\ttarget.copyWithin(insertionPosition + keysPosition, insertionPosition + 1, position)\n\t\t\t\ttarget.set(keysTarget.slice(0, keysPosition), insertionPosition)\n\t\t\t\tposition = newEnd\n\t\t\t} else {\n\t\t\t\ttarget[insertionOffset + start] = keysTarget[0]\n\t\t\t}\n\t\t}\n\t\tconst writeStruct = (object) => {\n\t\t\tlet newPosition = writeStructSlots(object, target, start, position, structures, makeRoom, (value, newPosition, notifySharedUpdate) => {\n\t\t\t\tif (notifySharedUpdate)\n\t\t\t\t\treturn hasSharedUpdate = true;\n\t\t\t\tposition = newPosition;\n\t\t\t\tlet startTarget = target;\n\t\t\t\tpack(value);\n\t\t\t\tresetStructures();\n\t\t\t\tif (startTarget !== target) {\n\t\t\t\t\treturn { position, targetView, target }; // indicate the buffer was re-allocated\n\t\t\t\t}\n\t\t\t\treturn position;\n\t\t\t}, this);\n\t\t\tif (newPosition === 0) // bail and go to a msgpack object\n\t\t\t\treturn writeObject(object);\n\t\t\tposition = newPosition;\n\t\t}\n\t}\n\tuseBuffer(buffer) {\n\t\t// this means we are finished using our own buffer and we can write over it safely\n\t\ttarget = buffer\n\t\ttarget.dataView || (target.dataView = new DataView(target.buffer, target.byteOffset, target.byteLength))\n\t\tposition = 0\n\t}\n\tset position (value) {\n\t\tposition = value;\n\t}\n\tget position() {\n\t\treturn position;\n\t}\n\tclearSharedData() {\n\t\tif (this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.typedStructs)\n\t\t\tthis.typedStructs = []\n\t}\n}\n\nextensionClasses = [ Date, Set, Error, RegExp, ArrayBuffer, Object.getPrototypeOf(Uint8Array.prototype).constructor /*TypedArray*/, DataView, C1Type ]\nextensions = [{\n\tpack(date, allocateForWrite, pack) {\n\t\tlet seconds = date.getTime() / 1000\n\t\tif ((this.useTimestamp32 || date.getMilliseconds() === 0) && seconds >= 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 32\n\t\t\tlet { target, targetView, position} = allocateForWrite(6)\n\t\t\ttarget[position++] = 0xd6\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, seconds)\n\t\t} else if (seconds > 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 64\n\t\t\tlet { target, targetView, position} = allocateForWrite(10)\n\t\t\ttarget[position++] = 0xd7\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 4000000 + ((seconds / 1000 / 0x100000000) >> 0))\n\t\t\ttargetView.setUint32(position + 4, seconds)\n\t\t} else if (isNaN(seconds)) {\n\t\t\tif (this.onInvalidDate) {\n\t\t\t\tallocateForWrite(0)\n\t\t\t\treturn pack(this.onInvalidDate())\n\t\t\t}\n\t\t\t// Intentionally invalid timestamp\n\t\t\tlet { target, targetView, position} = allocateForWrite(3)\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0xff\n\t\t\ttarget[position++] = 0xff\n\t\t} else {\n\t\t\t// Timestamp 96\n\t\t\tlet { target, targetView, position} = allocateForWrite(15)\n\t\t\ttarget[position++] = 0xc7\n\t\t\ttarget[position++] = 12\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 1000000)\n\t\t\ttargetView.setBigInt64(position + 4, BigInt(Math.floor(seconds)))\n\t\t}\n\t}\n}, {\n\tpack(set, allocateForWrite, pack) {\n\t\tif (this.setAsEmptyObject) {\n\t\t\tallocateForWrite(0);\n\t\t\treturn pack({})\n\t\t}\n\t\tlet array = Array.from(set)\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x73 // 's' for Set\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack(array)\n\t}\n}, {\n\tpack(error, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x65 // 'e' for error\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ error.name, error.message, error.cause ])\n\t}\n}, {\n\tpack(regex, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x78 // 'x' for regeXp\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ regex.source, regex.flags ])\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x10, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(typedArray, allocateForWrite) {\n\t\tlet constructor = typedArray.constructor\n\t\tif (constructor !== ByteArray && this.moreTypes)\n\t\t\twriteExtBuffer(typedArray, typedArrays.indexOf(constructor.name), allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(typedArray, allocateForWrite)\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x11, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(c1, allocateForWrite) { // specific 0xC1 object\n\t\tlet { target, position} = allocateForWrite(1)\n\t\ttarget[position] = 0xc1\n\t}\n}]\n\nfunction writeExtBuffer(typedArray, type, allocateForWrite, encode) {\n\tlet length = typedArray.byteLength\n\tif (length + 1 < 0x100) {\n\t\tvar { target, position } = allocateForWrite(4 + length)\n\t\ttarget[position++] = 0xc7\n\t\ttarget[position++] = length + 1\n\t} else if (length + 1 < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(5 + length)\n\t\ttarget[position++] = 0xc8\n\t\ttarget[position++] = (length + 1) >> 8\n\t\ttarget[position++] = (length + 1) & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(7 + length)\n\t\ttarget[position++] = 0xc9\n\t\ttargetView.setUint32(position, length + 1) // plus one for the type byte\n\t\tposition += 4\n\t}\n\ttarget[position++] = 0x74 // \"t\" for typed array\n\ttarget[position++] = type\n\tif (!typedArray.buffer) typedArray = new Uint8Array(typedArray)\n\ttarget.set(new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength), position)\n}\nfunction writeBuffer(buffer, allocateForWrite) {\n\tlet length = buffer.byteLength\n\tvar target, position\n\tif (length < 0x100) {\n\t\tvar { target, position } = allocateForWrite(length + 2)\n\t\ttarget[position++] = 0xc4\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(length + 3)\n\t\ttarget[position++] = 0xc5\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(length + 5)\n\t\ttarget[position++] = 0xc6\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\ttarget.set(buffer, position)\n}\n\nfunction writeExtensionData(result, target, position, type) {\n\tlet length = result.length\n\tswitch (length) {\n\t\tcase 1:\n\t\t\ttarget[position++] = 0xd4\n\t\t\tbreak\n\t\tcase 2:\n\t\t\ttarget[position++] = 0xd5\n\t\t\tbreak\n\t\tcase 4:\n\t\t\ttarget[position++] = 0xd6\n\t\t\tbreak\n\t\tcase 8:\n\t\t\ttarget[position++] = 0xd7\n\t\t\tbreak\n\t\tcase 16:\n\t\t\ttarget[position++] = 0xd8\n\t\t\tbreak\n\t\tdefault:\n\t\t\tif (length < 0x100) {\n\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\ttarget[position++] = length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xc8\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xc9\n\t\t\t\ttarget[position++] = length >> 24\n\t\t\t\ttarget[position++] = (length >> 16) & 0xff\n\t\t\t\ttarget[position++] = (length >> 8) & 0xff\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t}\n\t}\n\ttarget[position++] = type\n\ttarget.set(result, position)\n\tposition += length\n\treturn position\n}\n\nfunction insertIds(serialized, idsToInsert) {\n\t// insert the ids that need to be referenced for structured clones\n\tlet nextId\n\tlet distanceToMove = idsToInsert.length * 6\n\tlet lastEnd = serialized.length - distanceToMove\n\twhile (nextId = idsToInsert.pop()) {\n\t\tlet offset = nextId.offset\n\t\tlet id = nextId.id\n\t\tserialized.copyWithin(offset + distanceToMove, offset, lastEnd)\n\t\tdistanceToMove -= 6\n\t\tlet position = offset + distanceToMove\n\t\tserialized[position++] = 0xd6\n\t\tserialized[position++] = 0x69 // 'i'\n\t\tserialized[position++] = id >> 24\n\t\tserialized[position++] = (id >> 16) & 0xff\n\t\tserialized[position++] = (id >> 8) & 0xff\n\t\tserialized[position++] = id & 0xff\n\t\tlastEnd = offset\n\t}\n\treturn serialized\n}\n\nfunction writeBundles(start, pack, incrementPosition) {\n\tif (bundledStrings.length > 0) {\n\t\ttargetView.setUint32(bundledStrings.position + start, position + incrementPosition - bundledStrings.position - start)\n\t\tbundledStrings.stringsPosition = position - start;\n\t\tlet writeStrings = bundledStrings\n\t\tbundledStrings = null\n\t\tpack(writeStrings[0])\n\t\tpack(writeStrings[1])\n\t}\n}\n\nexport function addExtension(extension) {\n\tif (extension.Class) {\n\t\tif (!extension.pack && !extension.write)\n\t\t\tthrow new Error('Extension has no pack or write function')\n\t\tif (extension.pack && !extension.type)\n\t\t\tthrow new Error('Extension has no type (numeric code to identify the extension)')\n\t\textensionClasses.unshift(extension.Class)\n\t\textensions.unshift(extension)\n\t}\n\tunpackAddExtension(extension)\n}\nfunction prepareStructures(structures, packr) {\n\tstructures.isCompatible = (existingStructures) => {\n\t\tlet compatible = !existingStructures || ((packr.lastNamedStructuresLength || 0) === existingStructures.length)\n\t\tif (!compatible) // we want to merge these existing structures immediately since we already have it and we are in the right transaction\n\t\t\tpackr._mergeStructures(existingStructures);\n\t\treturn compatible;\n\t}\n\treturn structures\n}\nexport function setWriteStructSlots(writeSlots, makeStructures) {\n\twriteStructSlots = writeSlots;\n\tprepareStructures = makeStructures;\n}\n\nlet defaultPackr = new Packr({ useRecords: false })\nexport const pack = defaultPackr.pack\nexport const encode = defaultPackr.pack\nexport const Encoder = Packr\nexport { FLOAT32_OPTIONS } from './unpack.js'\nimport { FLOAT32_OPTIONS } from './unpack.js'\nexport const { NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } = FLOAT32_OPTIONS\nexport const REUSE_BUFFER_MODE = 512\nexport const RESET_BUFFER_MODE = 1024\nexport const RESERVE_START_SPACE = 2048", "\n/*\n\nFor \"any-data\":\n32-55 - record with record ids (-32)\n56 - 8-bit record ids\n57 - 16-bit record ids\n58 - 24-bit record ids\n59 - 32-bit record ids\n250-255 - followed by typed fixed width values\n64-250 msgpackr/cbor/paired data\narrays and strings within arrays are handled by paired encoding\n\nStructure encoding:\n(type - string (using paired encoding))+\n\nType encoding\nencoding byte - fixed width byte - next reference+\n\nEncoding byte:\nfirst bit:\n\t0 - inline\n\t1 - reference\nsecond bit:\n\t0 - data or number\n\t1 - string\n\nremaining bits:\n\tcharacter encoding - ISO-8859-x\n\n\nnull (0xff)+ 0xf6\nnull (0xff)+ 0xf7\n\n*/\n\n\nimport {setWriteStructSlots, RECORD_SYMBOL, addExtension} from './pack.js'\nimport {setReadStruct, mult10, readString} from './unpack.js';\nconst ASCII = 3; // the MIBenum from https://www.iana.org/assignments/character-sets/character-sets.xhtml (and other character encodings could be referenced by MIBenum)\nconst NUMBER = 0;\nconst UTF8 = 2;\nconst OBJECT_DATA = 1;\nconst DATE = 16;\nconst TYPE_NAMES = ['num', 'object', 'string', 'ascii'];\nTYPE_NAMES[DATE] = 'date';\nconst float32Headers = [false, true, true, false, false, true, true, false];\nlet evalSupported;\ntry {\n\tnew Function('');\n\tevalSupported = true;\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n}\n\nlet updatedPosition;\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nlet textEncoder, currentSource;\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nconst encodeUtf8 = hasNodeBuffer ? function(target, string, position) {\n\treturn target.utf8Write(string, position, target.byteLength - position)\n} : (textEncoder && textEncoder.encodeInto) ?\n\tfunction(target, string, position) {\n\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t} : false\n\nconst TYPE = Symbol('type');\nconst PARENT = Symbol('parent');\nsetWriteStructSlots(writeStruct, prepareStructures);\nfunction writeStruct(object, target, encodingStart, position, structures, makeRoom, pack, packr) {\n\tlet typedStructs = packr.typedStructs || (packr.typedStructs = []);\n\t// note that we rely on pack.js to load stored structures before we get to this point\n\tlet targetView = target.dataView;\n\tlet refsStartPosition = (typedStructs.lastStringStart || 100) + position;\n\tlet safeEnd = target.length - 10;\n\tlet start = position;\n\tif (position > safeEnd) {\n\t\ttarget = makeRoom(position);\n\t\ttargetView = target.dataView;\n\t\tposition -= encodingStart;\n\t\tstart -= encodingStart;\n\t\trefsStartPosition -= encodingStart;\n\t\tencodingStart = 0;\n\t\tsafeEnd = target.length - 10;\n\t}\n\n\tlet refOffset, refPosition = refsStartPosition;\n\n\tlet transition = typedStructs.transitions || (typedStructs.transitions = Object.create(null));\n\tlet nextId = typedStructs.nextId || typedStructs.length;\n\tlet headerSize =\n\t\tnextId < 0xf ? 1 :\n\t\t\tnextId < 0xf0 ? 2 :\n\t\t\t\tnextId < 0xf000 ? 3 :\n\t\t\t\t\tnextId < 0xf00000 ? 4 : 0;\n\tif (headerSize === 0)\n\t\treturn 0;\n\tposition += headerSize;\n\tlet queuedReferences = [];\n\tlet usedAscii0;\n\tlet keyIndex = 0;\n\tfor (let key in object) {\n\t\tlet value = object[key];\n\t\tlet nextTransition = transition[key];\n\t\tif (!nextTransition) {\n\t\t\ttransition[key] = nextTransition = {\n\t\t\t\tkey,\n\t\t\t\tparent: transition,\n\t\t\t\tenumerationOffset: 0,\n\t\t\t\tascii0: null,\n\t\t\t\tascii8: null,\n\t\t\t\tnum8: null,\n\t\t\t\tstring16: null,\n\t\t\t\tobject16: null,\n\t\t\t\tnum32: null,\n\t\t\t\tfloat64: null,\n\t\t\t\tdate64: null\n\t\t\t};\n\t\t}\n\t\tif (position > safeEnd) {\n\t\t\ttarget = makeRoom(position);\n\t\t\ttargetView = target.dataView;\n\t\t\tposition -= encodingStart;\n\t\t\tstart -= encodingStart;\n\t\t\trefsStartPosition -= encodingStart;\n\t\t\trefPosition -= encodingStart;\n\t\t\tencodingStart = 0;\n\t\t\tsafeEnd = target.length - 10\n\t\t}\n\t\tswitch (typeof value) {\n\t\t\tcase 'number':\n\t\t\t\tlet number = value;\n\t\t\t\t// first check to see if we are using a lot of ids and should default to wide/common format\n\t\t\t\tif (nextId < 200 || !nextTransition.num64) {\n\t\t\t\t\tif (number >> 0 === number && number < 0x20000000 && number > -0x1f000000) {\n\t\t\t\t\t\tif (number < 0xf6 && number >= 0 && (nextTransition.num8 && !(nextId > 200 && nextTransition.num32) || number < 0x20 && !nextTransition.num32)) {\n\t\t\t\t\t\t\ttransition = nextTransition.num8 || createTypeTransition(nextTransition, NUMBER, 1);\n\t\t\t\t\t\t\ttarget[position++] = number;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttransition = nextTransition.num32 || createTypeTransition(nextTransition, NUMBER, 4);\n\t\t\t\t\t\t\ttargetView.setUint32(position, number, true);\n\t\t\t\t\t\t\tposition += 4;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else if (number < 0x100000000 && number >= -0x80000000) {\n\t\t\t\t\t\ttargetView.setFloat32(position, number, true);\n\t\t\t\t\t\tif (float32Headers[target[position + 3] >>> 5]) {\n\t\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\tif (((xShifted = number * mult10[((target[position + 3] & 0x7f) << 1) | (target[position + 2] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\t\ttransition = nextTransition.num32 || createTypeTransition(nextTransition, NUMBER, 4);\n\t\t\t\t\t\t\t\tposition += 4;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition.num64 || createTypeTransition(nextTransition, NUMBER, 8);\n\t\t\t\ttargetView.setFloat64(position, number, true);\n\t\t\t\tposition += 8;\n\t\t\t\tbreak;\n\t\t\tcase 'string':\n\t\t\t\tlet strLength = value.length;\n\t\t\t\trefOffset = refPosition - refsStartPosition;\n\t\t\t\tif ((strLength << 2) + refPosition > safeEnd) {\n\t\t\t\t\ttarget = makeRoom((strLength << 2) + refPosition);\n\t\t\t\t\ttargetView = target.dataView;\n\t\t\t\t\tposition -= encodingStart;\n\t\t\t\t\tstart -= encodingStart;\n\t\t\t\t\trefsStartPosition -= encodingStart;\n\t\t\t\t\trefPosition -= encodingStart;\n\t\t\t\t\tencodingStart = 0;\n\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t}\n\t\t\t\tif (strLength > ((0xff00 + refOffset) >> 2)) {\n\t\t\t\t\tqueuedReferences.push(key, value, position - start);\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tlet isNotAscii\n\t\t\t\tlet strStart = refPosition;\n\t\t\t\tif (strLength < 0x40) {\n\t\t\t\t\tlet i, c1, c2;\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[refPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\trefPosition += encodeUtf8(target, value, refPosition);\n\t\t\t\t\tisNotAscii = refPosition - strStart > strLength;\n\t\t\t\t}\n\t\t\t\tif (refOffset < 0xa0 || (refOffset < 0xf6 && (nextTransition.ascii8 || nextTransition.string8))) {\n\t\t\t\t\t// short strings\n\t\t\t\t\tif (isNotAscii) {\n\t\t\t\t\t\tif (!(transition = nextTransition.string8)) {\n\t\t\t\t\t\t\tif (typedStructs.length > 10 && (transition = nextTransition.ascii8)) {\n\t\t\t\t\t\t\t\t// we can safely change ascii to utf8 in place since they are compatible\n\t\t\t\t\t\t\t\ttransition.__type = UTF8;\n\t\t\t\t\t\t\t\tnextTransition.ascii8 = null;\n\t\t\t\t\t\t\t\tnextTransition.string8 = transition;\n\t\t\t\t\t\t\t\tpack(null, 0, true); // special call to notify that structures have been updated\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttransition = createTypeTransition(nextTransition, UTF8, 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (refOffset === 0 && !usedAscii0) {\n\t\t\t\t\t\tusedAscii0 = true;\n\t\t\t\t\t\ttransition = nextTransition.ascii0 || createTypeTransition(nextTransition, ASCII, 0);\n\t\t\t\t\t\tbreak; // don't increment position\n\t\t\t\t\t}// else ascii:\n\t\t\t\t\telse if (!(transition = nextTransition.ascii8) && !(typedStructs.length > 10 && (transition = nextTransition.string8)))\n\t\t\t\t\t\ttransition = createTypeTransition(nextTransition, ASCII, 1);\n\t\t\t\t\ttarget[position++] = refOffset;\n\t\t\t\t} else {\n\t\t\t\t\t// TODO: Enable ascii16 at some point, but get the logic right\n\t\t\t\t\t//if (isNotAscii)\n\t\t\t\t\t\ttransition = nextTransition.string16 || createTypeTransition(nextTransition, UTF8, 2);\n\t\t\t\t\t//else\n\t\t\t\t\t\t//transition = nextTransition.ascii16 || createTypeTransition(nextTransition, ASCII, 2);\n\t\t\t\t\ttargetView.setUint16(position, refOffset, true);\n\t\t\t\t\tposition += 2;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'object':\n\t\t\t\tif (value) {\n\t\t\t\t\tif (value.constructor === Date) {\n\t\t\t\t\t\ttransition = nextTransition.date64 || createTypeTransition(nextTransition, DATE, 8);\n\t\t\t\t\t\ttargetView.setFloat64(position, value.getTime(), true);\n\t\t\t\t\t\tposition += 8;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tqueuedReferences.push(key, value, keyIndex);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t} else { // null\n\t\t\t\t\tnextTransition = anyType(nextTransition, position, targetView, -10); // match CBOR with this\n\t\t\t\t\tif (nextTransition) {\n\t\t\t\t\t\ttransition = nextTransition;\n\t\t\t\t\t\tposition = updatedPosition;\n\t\t\t\t\t} else queuedReferences.push(key, value, keyIndex);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'boolean':\n\t\t\t\ttransition = nextTransition.num8 || nextTransition.ascii8 || createTypeTransition(nextTransition, NUMBER, 1);\n\t\t\t\ttarget[position++] = value ? 0xf9 : 0xf8; // match CBOR with these\n\t\t\t\tbreak;\n\t\t\tcase 'undefined':\n\t\t\t\tnextTransition = anyType(nextTransition, position, targetView, -9); // match CBOR with this\n\t\t\t\tif (nextTransition) {\n\t\t\t\t\ttransition = nextTransition;\n\t\t\t\t\tposition = updatedPosition;\n\t\t\t\t} else queuedReferences.push(key, value, keyIndex);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tqueuedReferences.push(key, value, keyIndex);\n\t\t}\n\t\tkeyIndex++;\n\t}\n\n\tfor (let i = 0, l = queuedReferences.length; i < l;) {\n\t\tlet key = queuedReferences[i++];\n\t\tlet value = queuedReferences[i++];\n\t\tlet propertyIndex = queuedReferences[i++];\n\t\tlet nextTransition = transition[key];\n\t\tif (!nextTransition) {\n\t\t\ttransition[key] = nextTransition = {\n\t\t\t\tkey,\n\t\t\t\tparent: transition,\n\t\t\t\tenumerationOffset: propertyIndex - keyIndex,\n\t\t\t\tascii0: null,\n\t\t\t\tascii8: null,\n\t\t\t\tnum8: null,\n\t\t\t\tstring16: null,\n\t\t\t\tobject16: null,\n\t\t\t\tnum32: null,\n\t\t\t\tfloat64: null\n\t\t\t};\n\t\t}\n\t\tlet newPosition;\n\t\tif (value) {\n\t\t\t/*if (typeof value === 'string') { // TODO: we could re-enable long strings\n\t\t\t\tif (position + value.length * 3 > safeEnd) {\n\t\t\t\t\ttarget = makeRoom(position + value.length * 3);\n\t\t\t\t\tposition -= start;\n\t\t\t\t\ttargetView = target.dataView;\n\t\t\t\t\tstart = 0;\n\t\t\t\t}\n\t\t\t\tnewPosition = position + target.utf8Write(value, position, 0xffffffff);\n\t\t\t} else { */\n\t\t\tlet size;\n\t\t\trefOffset = refPosition - refsStartPosition;\n\t\t\tif (refOffset < 0xff00) {\n\t\t\t\ttransition = nextTransition.object16;\n\t\t\t\tif (transition)\n\t\t\t\t\tsize = 2;\n\t\t\t\telse if ((transition = nextTransition.object32))\n\t\t\t\t\tsize = 4;\n\t\t\t\telse {\n\t\t\t\t\ttransition = createTypeTransition(nextTransition, OBJECT_DATA, 2);\n\t\t\t\t\tsize = 2;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\ttransition = nextTransition.object32 || createTypeTransition(nextTransition, OBJECT_DATA, 4);\n\t\t\t\tsize = 4;\n\t\t\t}\n\t\t\tnewPosition = pack(value, refPosition);\n\t\t\t//}\n\t\t\tif (typeof newPosition === 'object') {\n\t\t\t\t// re-allocated\n\t\t\t\trefPosition = newPosition.position;\n\t\t\t\ttargetView = newPosition.targetView;\n\t\t\t\ttarget = newPosition.target;\n\t\t\t\trefsStartPosition -= encodingStart;\n\t\t\t\tposition -= encodingStart;\n\t\t\t\tstart -= encodingStart;\n\t\t\t\tencodingStart = 0;\n\t\t\t} else\n\t\t\t\trefPosition = newPosition;\n\t\t\tif (size === 2) {\n\t\t\t\ttargetView.setUint16(position, refOffset, true);\n\t\t\t\tposition += 2;\n\t\t\t} else {\n\t\t\t\ttargetView.setUint32(position, refOffset, true);\n\t\t\t\tposition += 4;\n\t\t\t}\n\t\t} else { // null or undefined\n\t\t\ttransition = nextTransition.object16 || createTypeTransition(nextTransition, OBJECT_DATA, 2);\n\t\t\ttargetView.setInt16(position, value === null ? -10 : -9, true);\n\t\t\tposition += 2;\n\t\t}\n\t\tkeyIndex++;\n\t}\n\n\n\tlet recordId = transition[RECORD_SYMBOL];\n\tif (recordId == null) {\n\t\trecordId = packr.typedStructs.length;\n\t\tlet structure = [];\n\t\tlet nextTransition = transition;\n\t\tlet key, type;\n\t\twhile ((type = nextTransition.__type) !== undefined) {\n\t\t\tlet size = nextTransition.__size;\n\t\t\tnextTransition = nextTransition.__parent;\n\t\t\tkey = nextTransition.key;\n\t\t\tlet property = [type, size, key];\n\t\t\tif (nextTransition.enumerationOffset)\n\t\t\t\tproperty.push(nextTransition.enumerationOffset);\n\t\t\tstructure.push(property);\n\t\t\tnextTransition = nextTransition.parent;\n\t\t}\n\t\tstructure.reverse();\n\t\ttransition[RECORD_SYMBOL] = recordId;\n\t\tpackr.typedStructs[recordId] = structure;\n\t\tpack(null, 0, true); // special call to notify that structures have been updated\n\t}\n\n\n\tswitch (headerSize) {\n\t\tcase 1:\n\t\t\tif (recordId >= 0x10) return 0;\n\t\t\ttarget[start] = recordId + 0x20;\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tif (recordId >= 0x100) return 0;\n\t\t\ttarget[start] = 0x38;\n\t\t\ttarget[start + 1] = recordId;\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tif (recordId >= 0x10000) return 0;\n\t\t\ttarget[start] = 0x39;\n\t\t\ttargetView.setUint16(start + 1, recordId, true);\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tif (recordId >= 0x1000000) return 0;\n\t\t\ttargetView.setUint32(start, (recordId << 8) + 0x3a, true);\n\t\t\tbreak;\n\t}\n\n\tif (position < refsStartPosition) {\n\t\tif (refsStartPosition === refPosition)\n\t\t\treturn position; // no refs\n\t\t// adjust positioning\n\t\ttarget.copyWithin(position, refsStartPosition, refPosition);\n\t\trefPosition += position - refsStartPosition;\n\t\ttypedStructs.lastStringStart = position - start;\n\t} else if (position > refsStartPosition) {\n\t\tif (refsStartPosition === refPosition)\n\t\t\treturn position; // no refs\n\t\ttypedStructs.lastStringStart = position - start;\n\t\treturn writeStruct(object, target, encodingStart, start, structures, makeRoom, pack, packr);\n\t}\n\treturn refPosition;\n}\nfunction anyType(transition, position, targetView, value) {\n\tlet nextTransition;\n\tif ((nextTransition = transition.ascii8 || transition.num8)) {\n\t\ttargetView.setInt8(position, value, true);\n\t\tupdatedPosition = position + 1;\n\t\treturn nextTransition;\n\t}\n\tif ((nextTransition = transition.string16 || transition.object16)) {\n\t\ttargetView.setInt16(position, value, true);\n\t\tupdatedPosition = position + 2;\n\t\treturn nextTransition;\n\t}\n\tif (nextTransition = transition.num32) {\n\t\ttargetView.setUint32(position, 0xe0000100 + value, true);\n\t\tupdatedPosition = position + 4;\n\t\treturn nextTransition;\n\t}\n\t// transition.float64\n\tif (nextTransition = transition.num64) {\n\t\ttargetView.setFloat64(position, NaN, true);\n\t\ttargetView.setInt8(position, value);\n\t\tupdatedPosition = position + 8;\n\t\treturn nextTransition;\n\t}\n\tupdatedPosition = position;\n\t// TODO: can we do an \"any\" type where we defer the decision?\n\treturn;\n}\nfunction createTypeTransition(transition, type, size) {\n\tlet typeName = TYPE_NAMES[type] + (size << 3);\n\tlet newTransition = transition[typeName] || (transition[typeName] = Object.create(null));\n\tnewTransition.__type = type;\n\tnewTransition.__size = size;\n\tnewTransition.__parent = transition;\n\treturn newTransition;\n}\nfunction onLoadedStructures(sharedData) {\n\tif (!(sharedData instanceof Map))\n\t\treturn sharedData;\n\tlet typed = sharedData.get('typed') || [];\n\tif (Object.isFrozen(typed))\n\t\ttyped = typed.map(structure => structure.slice(0));\n\tlet named = sharedData.get('named');\n\tlet transitions = Object.create(null);\n\tfor (let i = 0, l = typed.length; i < l; i++) {\n\t\tlet structure = typed[i];\n\t\tlet transition = transitions;\n\t\tfor (let [type, size, key] of structure) {\n\t\t\tlet nextTransition = transition[key];\n\t\t\tif (!nextTransition) {\n\t\t\t\ttransition[key] = nextTransition = {\n\t\t\t\t\tkey,\n\t\t\t\t\tparent: transition,\n\t\t\t\t\tenumerationOffset: 0,\n\t\t\t\t\tascii0: null,\n\t\t\t\t\tascii8: null,\n\t\t\t\t\tnum8: null,\n\t\t\t\t\tstring16: null,\n\t\t\t\t\tobject16: null,\n\t\t\t\t\tnum32: null,\n\t\t\t\t\tfloat64: null,\n\t\t\t\t\tdate64: null,\n\t\t\t\t};\n\t\t\t}\n\t\t\ttransition = createTypeTransition(nextTransition, type, size);\n\t\t}\n\t\ttransition[RECORD_SYMBOL] = i;\n\t}\n\ttyped.transitions = transitions;\n\tthis.typedStructs = typed;\n\tthis.lastTypedStructuresLength = typed.length;\n\treturn named;\n}\nvar sourceSymbol = Symbol.for('source')\nfunction readStruct(src, position, srcEnd, unpackr) {\n\tlet recordId = src[position++] - 0x20;\n\tif (recordId >= 24) {\n\t\tswitch(recordId) {\n\t\t\tcase 24: recordId = src[position++]; break;\n\t\t\t// little endian:\n\t\t\tcase 25: recordId = src[position++] + (src[position++] << 8); break;\n\t\t\tcase 26: recordId = src[position++] + (src[position++] << 8) + (src[position++] << 16); break;\n\t\t\tcase 27: recordId = src[position++] + (src[position++] << 8) + (src[position++] << 16) + (src[position++] << 24); break;\n\t\t}\n\t}\n\tlet structure = unpackr.typedStructs && unpackr.typedStructs[recordId];\n\tif (!structure) {\n\t\t// copy src buffer because getStructures will override it\n\t\tsrc = Uint8Array.prototype.slice.call(src, position, srcEnd);\n\t\tsrcEnd -= position;\n\t\tposition = 0;\n\t\tif (!unpackr.getStructures)\n\t\t\tthrow new Error(`Reference to shared structure ${recordId} without getStructures method`);\n\t\tunpackr._mergeStructures(unpackr.getStructures());\n\t\tif (!unpackr.typedStructs)\n\t\t\tthrow new Error('Could not find any shared typed structures');\n\t\tunpackr.lastTypedStructuresLength = unpackr.typedStructs.length;\n\t\tstructure = unpackr.typedStructs[recordId];\n\t\tif (!structure)\n\t\t\tthrow new Error('Could not find typed structure ' + recordId);\n\t}\n\tvar construct = structure.construct;\n\tvar fullConstruct = structure.fullConstruct;\n\tif (!construct) {\n\t\tconstruct = structure.construct = function LazyObject() {\n\t\t}\n\t\tfullConstruct = structure.fullConstruct = function LoadedObject() {\n\t\t}\n\t\tfullConstruct.prototype = unpackr.structPrototype ?? {};\n\t\tvar prototype = construct.prototype = unpackr.structPrototype ? Object.create(unpackr.structPrototype) : {};\n\t\tlet properties = [];\n\t\tlet currentOffset = 0;\n\t\tlet lastRefProperty;\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet definition = structure[i];\n\t\t\tlet [ type, size, key, enumerationOffset ] = definition;\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tlet property = {\n\t\t\t\tkey,\n\t\t\t\toffset: currentOffset,\n\t\t\t}\n\t\t\tif (enumerationOffset)\n\t\t\t\tproperties.splice(i + enumerationOffset, 0, property);\n\t\t\telse\n\t\t\t\tproperties.push(property);\n\t\t\tlet getRef;\n\t\t\tswitch(size) { // TODO: Move into a separate function\n\t\t\t\tcase 0: getRef = () => 0; break;\n\t\t\t\tcase 1:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet ref = source.bytes[position + property.offset];\n\t\t\t\t\t\treturn ref >= 0xf6 ? toConstant(ref) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\tlet ref = dataView.getUint16(position + property.offset, true);\n\t\t\t\t\t\treturn ref >= 0xff00 ? toConstant(ref & 0xff) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\tlet ref = dataView.getUint32(position + property.offset, true);\n\t\t\t\t\t\treturn ref >= 0xffffff00 ? toConstant(ref & 0xff) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tproperty.getRef = getRef;\n\t\t\tcurrentOffset += size;\n\t\t\tlet get;\n\t\t\tswitch(type) {\n\t\t\t\tcase ASCII:\n\t\t\t\t\tif (lastRefProperty && !lastRefProperty.next)\n\t\t\t\t\t\tlastRefProperty.next = property;\n\t\t\t\t\tlastRefProperty = property;\n\t\t\t\t\tproperty.multiGetCount = 0;\n\t\t\t\t\tget = function(source) {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet position = source.position;\n\t\t\t\t\t\tlet refStart = currentOffset + position;\n\t\t\t\t\t\tlet ref = getRef(source, position);\n\t\t\t\t\t\tif (typeof ref !== 'number') return ref;\n\n\t\t\t\t\t\tlet end, next = property.next;\n\t\t\t\t\t\twhile(next) {\n\t\t\t\t\t\t\tend = next.getRef(source, position);\n\t\t\t\t\t\t\tif (typeof end === 'number')\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tend = null;\n\t\t\t\t\t\t\tnext = next.next;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (end == null)\n\t\t\t\t\t\t\tend = source.bytesEnd - refStart;\n\t\t\t\t\t\tif (source.srcString) {\n\t\t\t\t\t\t\treturn source.srcString.slice(ref, end);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t/*if (property.multiGetCount > 0) {\n\t\t\t\t\t\t\tlet asciiEnd;\n\t\t\t\t\t\t\tnext = firstRefProperty;\n\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\tasciiEnd = dataView.getUint16(source.position + next.offset, true);\n\t\t\t\t\t\t\t\tif (asciiEnd < 0xff00)\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\t\tasciiEnd = null;\n\t\t\t\t\t\t\t} while((next = next.next));\n\t\t\t\t\t\t\tif (asciiEnd == null)\n\t\t\t\t\t\t\t\tasciiEnd = source.bytesEnd - refStart\n\t\t\t\t\t\t\tsource.srcString = src.toString('latin1', refStart, refStart + asciiEnd);\n\t\t\t\t\t\t\treturn source.srcString.slice(ref, end);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (source.prevStringGet) {\n\t\t\t\t\t\t\tsource.prevStringGet.multiGetCount += 2;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tsource.prevStringGet = property;\n\t\t\t\t\t\t\tproperty.multiGetCount--;\n\t\t\t\t\t\t}*/\n\t\t\t\t\t\treturn readString(src, ref + refStart, end - ref);\n\t\t\t\t\t\t//return src.toString('latin1', ref + refStart, end + refStart);\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase UTF8: case OBJECT_DATA:\n\t\t\t\t\tif (lastRefProperty && !lastRefProperty.next)\n\t\t\t\t\t\tlastRefProperty.next = property;\n\t\t\t\t\tlastRefProperty = property;\n\t\t\t\t\tget = function(source) {\n\t\t\t\t\t\tlet position = source.position;\n\t\t\t\t\t\tlet refStart = currentOffset + position;\n\t\t\t\t\t\tlet ref = getRef(source, position);\n\t\t\t\t\t\tif (typeof ref !== 'number') return ref;\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet end, next = property.next;\n\t\t\t\t\t\twhile(next) {\n\t\t\t\t\t\t\tend = next.getRef(source, position);\n\t\t\t\t\t\t\tif (typeof end === 'number')\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tend = null;\n\t\t\t\t\t\t\tnext = next.next;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (end == null)\n\t\t\t\t\t\t\tend = source.bytesEnd - refStart;\n\t\t\t\t\t\tif (type === UTF8) {\n\t\t\t\t\t\t\treturn src.toString('utf8', ref + refStart, end + refStart);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcurrentSource = source;\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\treturn unpackr.unpack(src, { start: ref + refStart, end: end + refStart });\n\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\tcurrentSource = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase NUMBER:\n\t\t\t\t\tswitch(size) {\n\t\t\t\t\t\tcase 4:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\t\tlet position = source.position + property.offset;\n\t\t\t\t\t\t\t\tlet value = dataView.getInt32(position, true)\n\t\t\t\t\t\t\t\tif (value < 0x20000000) {\n\t\t\t\t\t\t\t\t\tif (value > -0x1f000000)\n\t\t\t\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t\t\t\tif (value > -0x20000000)\n\t\t\t\t\t\t\t\t\t\treturn toConstant(value & 0xff);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet fValue = dataView.getFloat32(position, true);\n\t\t\t\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\tlet multiplier = mult10[((src[position + 3] & 0x7f) << 1) | (src[position + 2] >> 7)]\n\t\t\t\t\t\t\t\treturn ((multiplier * fValue + (fValue > 0 ? 0.5 : -0.5)) >> 0) / multiplier;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 8:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\t\tlet value = dataView.getFloat64(source.position + property.offset, true);\n\t\t\t\t\t\t\t\tif (isNaN(value)) {\n\t\t\t\t\t\t\t\t\tlet byte = src[source.position + property.offset];\n\t\t\t\t\t\t\t\t\tif (byte >= 0xf6)\n\t\t\t\t\t\t\t\t\t\treturn toConstant(byte);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet value = src[source.position + property.offset];\n\t\t\t\t\t\t\t\treturn value < 0xf6 ? value : toConstant(value);\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase DATE:\n\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\treturn new Date(dataView.getFloat64(source.position + property.offset, true));\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\t\t\tproperty.get = get;\n\t\t}\n\t\t// TODO: load the srcString for faster string decoding on toJSON\n\t\tif (evalSupported) {\n\t\t\tlet objectLiteralProperties = [];\n\t\t\tlet args = [];\n\t\t\tlet i = 0;\n\t\t\tlet hasInheritedProperties;\n\t\t\tfor (let property of properties) { // assign in enumeration order\n\t\t\t\tif (unpackr.alwaysLazyProperty && unpackr.alwaysLazyProperty(property.key)) {\n\t\t\t\t\t// these properties are not eagerly evaluated and this can be used for creating properties\n\t\t\t\t\t// that are not serialized as JSON\n\t\t\t\t\thasInheritedProperties = true;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tObject.defineProperty(prototype, property.key, { get: withSource(property.get), enumerable: true });\n\t\t\t\tlet valueFunction = 'v' + i++;\n\t\t\t\targs.push(valueFunction);\n\t\t\t\tobjectLiteralProperties.push('o[' + JSON.stringify(property.key) + ']=' + valueFunction + '(s)');\n\t\t\t}\n\t\t\tif (hasInheritedProperties) {\n\t\t\t\tobjectLiteralProperties.push('__proto__:this');\n\t\t\t}\n\t\t\tlet toObject = (new Function(...args, 'var c=this;return function(s){var o=new c();' + objectLiteralProperties.join(';') + ';return o;}')).apply(fullConstruct, properties.map(prop => prop.get));\n\t\t\tObject.defineProperty(prototype, 'toJSON', {\n\t\t\t\tvalue(omitUnderscoredProperties) {\n\t\t\t\t\treturn toObject.call(this, this[sourceSymbol]);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tObject.defineProperty(prototype, 'toJSON', {\n\t\t\t\tvalue(omitUnderscoredProperties) {\n\t\t\t\t\t// return an enumerable object with own properties to JSON stringify\n\t\t\t\t\tlet resolved = {};\n\t\t\t\t\tfor (let i = 0, l = properties.length; i < l; i++) {\n\t\t\t\t\t\t// TODO: check alwaysLazyProperty\n\t\t\t\t\t\tlet key = properties[i].key;\n\n\t\t\t\t\t\tresolved[key] = this[key];\n\t\t\t\t\t}\n\t\t\t\t\treturn resolved;\n\t\t\t\t},\n\t\t\t\t// not enumerable or anything\n\t\t\t});\n\t\t}\n\t}\n\tvar instance = new construct();\n\tinstance[sourceSymbol] = {\n\t\tbytes: src,\n\t\tposition,\n\t\tsrcString: '',\n\t\tbytesEnd: srcEnd\n\t}\n\treturn instance;\n}\nfunction toConstant(code) {\n\tswitch(code) {\n\t\tcase 0xf6: return null;\n\t\tcase 0xf7: return undefined;\n\t\tcase 0xf8: return false;\n\t\tcase 0xf9: return true;\n\t}\n\tthrow new Error('Unknown constant');\n}\nfunction withSource(get) {\n\treturn function() {\n\t\treturn get(this[sourceSymbol]);\n\t}\n}\n\nfunction saveState() {\n\tif (currentSource) {\n\t\tcurrentSource.bytes = Uint8Array.prototype.slice.call(currentSource.bytes, currentSource.position, currentSource.bytesEnd);\n\t\tcurrentSource.position = 0;\n\t\tcurrentSource.bytesEnd = currentSource.bytes.length;\n\t}\n}\nfunction prepareStructures(structures, packr) {\n\tif (packr.typedStructs) {\n\t\tlet structMap = new Map();\n\t\tstructMap.set('named', structures);\n\t\tstructMap.set('typed', packr.typedStructs);\n\t\tstructures = structMap;\n\t}\n\tlet lastTypedStructuresLength = packr.lastTypedStructuresLength || 0;\n\tstructures.isCompatible = existing => {\n\t\tlet compatible = true;\n\t\tif (existing instanceof Map) {\n\t\t\tlet named = existing.get('named') || [];\n\t\t\tif (named.length !== (packr.lastNamedStructuresLength || 0))\n\t\t\t\tcompatible = false;\n\t\t\tlet typed = existing.get('typed') || [];\n\t\t\tif (typed.length !== lastTypedStructuresLength)\n\t\t\t\tcompatible = false;\n\t\t} else if (existing instanceof Array || Array.isArray(existing)) {\n\t\t\tif (existing.length !== (packr.lastNamedStructuresLength || 0))\n\t\t\t\tcompatible = false;\n\t\t}\n\t\tif (!compatible)\n\t\t\tpackr._mergeStructures(existing);\n\t\treturn compatible;\n\t};\n\tpackr.lastTypedStructuresLength = packr.typedStructs && packr.typedStructs.length;\n\treturn structures;\n}\n\nsetReadStruct(readStruct, onLoadedStructures, saveState);\n\n", "export { Packr, Encoder, addExtension, pack, encode, NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } from './pack.js'\nexport { Unpackr, Decoder, C1, unpack, unpackMultiple, decode, FLOAT32_OPTIONS, clearSource, roundFloat32, isNativeAccelerationEnabled } from './unpack.js'\nimport './struct.js'\nexport { PackrStream, UnpackrStream, PackrStream as EncoderStream, UnpackrStream as DecoderStream } from './stream.js'\nexport { decodeIter, encodeIter } from './iterators.js'\nexport const useRecords = false\nexport const mapsAsObjects = true\nimport { setExtractor } from './unpack.js'\nimport { createRequire } from 'module'\n\nconst nativeAccelerationDisabled = process.env.MSGPACKR_NATIVE_ACCELERATION_DISABLED !== undefined && process.env.MSGPACKR_NATIVE_ACCELERATION_DISABLED.toLowerCase() === 'true';\n\nif (!nativeAccelerationDisabled) {\n\tlet extractor\n\ttry {\n\t\tif (typeof require == 'function')\n\t\t\textractor = require('msgpackr-extract')\n\t\telse\n\t\t\textractor = createRequire(import.meta.url)('msgpackr-extract')\n\t\tif (extractor)\n\t\t\tsetExtractor(extractor.extractStrings)\n\t} catch (error) {\n\t\t// native module is optional\n\t}\n}", "import chai from 'chai';\nimport * as msgpackr from '../node-index.js';\nimport '../struct.js';\n//inspector.open(9229, null, true); debugger\nimport { readFileSync } from 'fs';\nlet allSampleData = [];\nfor (let i = 1; i < 6; i++) {\n\tallSampleData.push(JSON.parse(readFileSync(new URL(`./example${i > 1 ? i : ''}.json`, import.meta.url))));\n}\nallSampleData.push({\n\tname: 'some other types',\n\tdate: new Date(),\n\tempty: '',\n})\nconst sampleData = allSampleData[3];\nfunction tryRequire(module) {\n\ttry {\n\t\treturn require(module)\n\t} catch(error) {\n\t\treturn {}\n\t}\n}\n\nlet seed = 0;\nfunction random() {\n\tseed++;\n\tlet a = seed * 15485863;\n\treturn (a * a * a % 2038074743) / 2038074743;\n}\n//if (typeof chai === 'undefined') { chai = require('chai') }\nvar assert = chai.assert\n//if (typeof msgpackr === 'undefined') { msgpackr = require('..') }\nvar Packr = msgpackr.Packr\nvar Unpackr = msgpackr.Unpackr\nvar unpack = msgpackr.unpack\nvar unpackMultiple = msgpackr.unpackMultiple\nvar roundFloat32 = msgpackr.roundFloat32\nvar pack = msgpackr.pack\nvar DECIMAL_FIT = msgpackr.FLOAT32_OPTIONS.DECIMAL_FIT\n\nvar addExtension = msgpackr.addExtension\nvar zlib = tryRequire('zlib')\nvar deflateSync = zlib.deflateSync\nvar inflateSync = zlib.inflateSync\nvar deflateSync = zlib.brotliCompressSync\nvar inflateSync = zlib.brotliDecompressSync\nvar constants = zlib.constants\ntry {\n//\tvar { decode, encode } = require('msgpack-lite')\n} catch (error) {}\n\nvar ITERATIONS = 4000\n\nclass ExtendArray extends Array {\n}\n\nclass ExtendArray2 extends Array {\n}\n\nclass ExtendArray3 extends Array {\n}\n\n\nclass ExtendObject {\n}\n\n\nsuite('msgpackr basic tests', function() {\n\ttest('pack/unpack data', function () {\n\t\tvar data = {\n\t\t\tdata: [\n\t\t\t\t{a: 1, name: 'one', type: 'odd', isOdd: true},\n\t\t\t\t{a: 2, name: 'two', type: 'even'},\n\t\t\t\t{a: 3, name: 'three', type: 'odd', isOdd: true},\n\t\t\t\t{a: 4, name: 'four', type: 'even'},\n\t\t\t\t{a: 5, name: 'five', type: 'odd', isOdd: true},\n\t\t\t\t{a: 6, name: 'six', type: 'even', isOdd: null}\n\t\t\t],\n\t\t\tdescription: 'some names',\n\t\t\ttypes: ['odd', 'even'],\n\t\t\tconvertEnumToNum: [\n\t\t\t\t{prop: 'test'},\n\t\t\t\t{prop: 'test'},\n\t\t\t\t{prop: 'test'},\n\t\t\t\t{prop: 1},\n\t\t\t\t{prop: 2},\n\t\t\t\t{prop: [undefined]},\n\t\t\t\t{prop: null}\n\t\t\t]\n\t\t}\n\t\tlet structures = []\n\t\tlet packr = new Packr({structures})\n\t\tvar serialized = packr.pack(data)\n\t\tserialized = packr.pack(data)\n\t\tserialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\n\ttest('mixed structures', function () {\n\t\tlet data1 = {a: 1, b: 2, c: 3}\n\t\tlet data2 = {a: 1, b: 2, d: 4}\n\t\tlet data3 = {a: 1, b: 2, e: 5}\n\t\tlet structures = []\n\t\tlet packr = new Packr({structures})\n\t\tvar serialized = packr.pack(data1)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data1)\n\t\tvar serialized = packr.pack(data2)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data2)\n\t\tvar serialized = packr.pack(data3)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data3)\n\t})\n\n\ttest('mixed array', function () {\n\t\tvar data = [\n\t\t\t'one',\n\t\t\t'two',\n\t\t\t'one',\n\t\t\t10,\n\t\t\t11,\n\t\t\tnull,\n\t\t\ttrue,\n\t\t\t'three',\n\t\t\t'three',\n\t\t\t'one', [\n\t\t\t\t3, -5, -50, -400, 1.3, -5.3, true\n\t\t\t]\n\t\t]\n\t\tlet structures = []\n\t\tlet packr = new Packr({structures})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('255 chars', function () {\n\t\tconst data = 'RRZG9A6I7xupPeOZhxcOcioFsuhszGOdyDUcbRf4Zef2kdPIfC9RaLO4jTM5JhuZvTsF09fbRHMGtqk7YAgu3vespeTe9l61ziZ6VrMnYu2CamK96wCkmz0VUXyqaiUoTPgzk414LS9yYrd5uh7w18ksJF5SlC2e91rukWvNqAZJjYN3jpkqHNOFchCwFrhbxq2Lrv1kSJPYCx9blRg2hGmYqTbElLTZHv20iNqwZeQbRMgSBPT6vnbCBPnOh1W'\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.equal(deserialized, data)\n\t})\n\ttest('use ArrayBuffer', function () {\n\t\tconst data = {prop: 'a test'};\n\t\tvar serialized = pack(data)\n\t\tlet ab = new ArrayBuffer(serialized.length);\n\t\tlet u8 = new Uint8Array(ab);\n\t\tu8.set(serialized);\n\t\tvar deserialized = unpack(ab)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('pack/unpack varying data with random access structures', function () {\n\t\tlet structures = []\n\t\tlet packr = new Packr({\n\t\t\tstructures, useRecords: true, randomAccessStructure: true, freezeData: true, saveStructures(structures) {\n\t\t\t}, getStructures() {\n\t\t\t\tconsole.log('getStructures');\n\t\t\t}\n\t\t})\n\t\tfor (let i = 0; i < 2000; i++) {\n\t\t\tlet data = {};\n\t\t\tlet props = ['foo', 'bar', 'a', 'b', 'c', 'name', 'age', 'd'];\n\n\t\t\tfunction makeString() {\n\t\t\t\tlet str = '';\n\t\t\t\twhile (random() < 0.9) {\n\t\t\t\t\tstr += random() < 0.8 ? 'hello world' : String.fromCharCode(300);\n\t\t\t\t}\n\t\t\t\treturn str;\n\t\t\t}\n\n\t\t\tfor (let i = 0; i < random() * 20; i++) {\n\t\t\t\tdata[props[Math.floor(random() * 8)]] =\n\t\t\t\t\trandom() < 0.3 ? Math.floor(random() * 400) / 2 :\n\t\t\t\t\t\trandom() < 0.3 ? makeString() : random() < 0.3 ? true : random() < 0.3 ? sampleData : null;\n\t\t\t}\n\t\t\tvar serialized = packr.pack(data)\n\t\t\tvar deserialized = packr.unpack(serialized);\n\t\t\tfor (let key in deserialized) {\n\t\t\t\tlet a = deserialized[key];\n\t\t\t}\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t}\n\t})\n\n\tfor (let sampleData of allSampleData) {\n\t\tlet snippet = JSON.stringify(sampleData).slice(0, 20) + '...';\n\t\ttest('pack/unpack sample data ' + snippet, function () {\n\t\t\tvar data = sampleData\n\t\t\tlet structures = []\n\t\t\tvar serialized = pack(data)\n\t\t\tvar deserialized = unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t\tvar serialized = pack(data)\n\t\t\tvar deserialized = unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t})\n\t\ttest('pack/unpack sample data with Uint8Array encoding' + snippet, function () {\n\t\t\tvar data = sampleData\n\t\t\tlet structures = []\n\t\t\tvar serialized = pack(data)\n\t\t\tserialized = new Uint8Array(serialized)\n\t\t\tvar deserialized = unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t\tvar serialized = pack(data)\n\t\t\tvar deserialized = unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t})\n\t\ttest('pack/unpack sample data with random access structures ' + snippet, function () {\n\t\t\tvar data = sampleData\n\t\t\tlet structures = []\n\t\t\tlet packr = new Packr({\n\t\t\t\tstructures, useRecords: true, randomAccessStructure: true, freezeData: true, saveStructures(structures) {\n\t\t\t\t}, getStructures() {\n\t\t\t\t\tconsole.log('getStructures');\n\t\t\t\t}\n\t\t\t})\n\t\t\tfor (let i = 0; i < 20; i++) {\n\t\t\t\tvar serialized = packr.pack(data)\n\t\t\t\tvar deserialized = packr.unpack(serialized, {lazy: true});\n\t\t\t\tvar copied = {}\n\t\t\t\tfor (let key in deserialized) {\n\t\t\t\t\tcopied[key] = deserialized[key];\n\t\t\t\t}\n\t\t\t\tassert.deepEqual(copied, data)\n\t\t\t}\n\t\t})\n\t\ttest('pack/unpack sample data with bundled strings ' + snippet, function () {\n\t\t\tvar data = sampleData\n\t\t\tlet packr = new Packr({ /*structures,*/ useRecords: false, bundleStrings: true})\n\t\t\tvar serialized = packr.pack(data)\n\t\t\tvar deserialized = packr.unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\n\t\t});\n\t}\n\n\ttest('pack/unpack sample data with useRecords function', function () {\n\t\tvar data = [\n\t\t\t{id: 1, type: 1, labels: {a: 1, b: 2}},\n\t\t\t{id: 2, type: 1, labels: {b: 1, c: 2}},\n\t\t\t{id: 3, type: 1, labels: {d: 1, e: 2}}\n\t\t]\n\t\t\n\t\tvar alternatives = [\n\t\t\t{useRecords: false}, // 88 bytes\n\t\t\t{useRecords: true}, // 58 bytes\n\t\t\t{mapsAsObjects: true, useRecords: (v)=>!!v.id}, // 55 bytes\n\t\t\t{mapsAsObjects: true, variableMapSize: true, useRecords: (v)=>!!v.id} // 49 bytes\n\t\t]\n\n\t\tfor(let o of alternatives) {\n\t\t\tlet packr = new Packr(o)\n\t\t\tvar serialized = packr.pack(data)\n\t\t\tvar deserialized = packr.unpack(serialized)\n\t\t\tassert.deepEqual(deserialized, data)\t\n\t\t}\n\t})\n\n\ttest('mapAsEmptyObject combination', function () {\n\t\tconst msgpackr = new Packr({ useRecords: false, encodeUndefinedAsNil: true, variableMapSize: true, mapAsEmptyObject: true, setAsEmptyObject: true  });\n\n\t\tconst map = new Map();\n\t\tmap.set('a', 1);\n\t\tmap.set('b', 2);\n\t\tconst set = new Set();\n\t\tset.add('a');\n\t\tset.add('b');\n\t\tconst input = { map, set };\n\n\t\tconst packed = msgpackr.pack(input);\n\t\tconst unpacked = msgpackr.unpack(packed);\n\t\tassert.deepEqual(unpacked.map, {});\n\t\tassert.deepEqual(unpacked.set, {});\n\t});\n\ttest('pack/unpack numeric coercible keys', function () {\n\t\tvar data = { a: 1, 2: 'test', '-3.45': 'test2'}\n\t\tlet packr = new Packr({variableMapSize: true, coercibleKeyAsNumber: true, useRecords: false});\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('pack/unpack empty data with bundled strings', function () {\n\t\tvar data = {}\n\t\tlet packr = new Packr({bundleStrings: true})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('pack/unpack large amount of chinese characters', function() {\n\t\tconst MSGPACK_OPTIONS = {bundleStrings: true}\n\n\t\tconst item = {\n\t\t\tmessage: '你好你好你好你好你好你好你好你好你好', // some Chinese characters\n\t\t}\n\n\t\ttestSize(100)\n\t\ttestSize(1000)\n\t\ttestSize(10000)\n\t\tfunction testSize(size) {\n\t\t\tconst list = []\n\t\t\tfor (let i = 0; i < size; i++) {\n\t\t\t\tlist.push({...item})\n\t\t\t}\n\n\t\t\tconst packer = new Packr(MSGPACK_OPTIONS)\n\t\t\tconst unpacker = new Unpackr(MSGPACK_OPTIONS)\n\t\t\tconst encoded = packer.pack(list)\n\t\t\tconst decoded = unpacker.unpack(encoded)\n\t\t\tassert.deepEqual(list, decoded);\n\t\t}\n\t});\n\ttest('pack/unpack sequential data', function () {\n\t\tvar data = {foo: 1, bar: 2}\n\t\tlet packr = new Packr({sequential: true})\n\t\tlet unpackr = new Unpackr({sequential: true})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = unpackr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = unpackr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('pack/unpack with bundled strings and sequential', function () {\n\t\tconst options = {\n\t\t\tbundleStrings: true,\n\t\t\tsequential: true,\n\t\t};\n\n\t\tconst packer = new Packr(options);\n\t\tconst unpacker = new Packr(options);\n\n\t\tconst data = {data: 42}; // key length >= 4\n\n\t\tunpacker.unpackMultiple(Buffer.concat([\n\t\t\tpacker.pack(data),\n\t\t\tpacker.pack(data)\n\t\t]));\n\t});\n\tif (typeof Buffer != 'undefined')\n\ttest('replace data', function(){\n\t\tvar data1 = {\n\t\t\tdata: [\n\t\t\t\t{ a: 1, name: 'one', type: 'odd', isOdd: true, a: '13 characters' },\n\t\t\t\t{ a: 2, name: 'two', type: 'even', a: '11 characte' },\n\t\t\t\t{ a: 3, name: 'three', type: 'odd', isOdd: true, a: '12 character' },\n\t\t\t\t{ a: 4, name: 'four', type: 'even', a: '9 charact'},\n\t\t\t\t{ a: 5, name: 'five', type: 'odd', isOdd: true, a: '14 characters!' },\n\t\t\t\t{ a: 6, name: 'six', type: 'even', isOdd: null }\n\t\t\t],\n\t\t}\n\t\tvar data2 = {\n\t\t\tdata: [\n\t\t\t\t{ foo: 7, name: 'one', type: 'odd', isOdd: true },\n\t\t\t\t{ foo: 8, name: 'two', type: 'even'},\n\t\t\t\t{ foo: 9, name: 'three', type: 'odd', isOdd: true },\n\t\t\t\t{ foo: 10, name: 'four', type: 'even'},\n\t\t\t\t{ foo: 11, name: 'five', type: 'odd', isOdd: true },\n\t\t\t\t{ foo: 12, name: 'six', type: 'even', isOdd: null }\n\t\t\t],\n\t\t}\n\t\tvar serialized1 = pack(data1)\n\t\tvar serialized2 = pack(data2)\n\t\tvar b = Buffer.alloc(8000)\n\t\tserialized1.copy(b)\n\t\tvar deserialized1 = unpack(b, serialized1.length)\n\t\tserialized2.copy(b)\n\t\tvar deserialized2 = unpack(b, serialized2.length)\n\t\tassert.deepEqual(deserialized1, data1)\n\t\tassert.deepEqual(deserialized2, data2)\n\t})\n\n\ttest('compact 123', function() {\n\t\tassert.equal(pack(123).length, 1)\n\t})\n\n\ttest('BigInt', function() {\n\t\tlet packr = new Packr({useBigIntExtension: true})\n\t\tlet data = {\n\t\t\ta: 3333333333333333333333333333n,\n\t\t\tb: 1234567890123456789012345678901234567890n,\n\t\t\tc: -3333333333333333333333333333n,\n\t\t\td: -352523523642364364364264264264264264262642642n,\n\t\t\te: 0xffffffffffffffffffffffffffn,\n\t\t\tf: -0xffffffffffffffffffffffffffn,\n\t\t\to: -12345678901234567890n,\n\t\t\tarray: [],\n\t\t}\n\t\t\n\t\tlet serialized = packr.pack(data)\n\t\tlet deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(data, deserialized)\n\t})\n\n\n\ttest('extended class pack/unpack', function(){\n\t\tfunction Extended() {\n\n\t\t}\n\t\tExtended.prototype.getDouble = function() {\n\t\t\treturn this.value * 2\n\t\t}\n\t\tvar instance = new Extended()\n\t\tinstance.value = 4\n\t\tinstance.string = 'decode this: ᾜ'\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: Extended,\n\t\t\ttype: 11,\n\t\t\tunpack: function(buffer) {\n\t\t\t\tlet e = new Extended()\n\t\t\t\tlet data = packr.unpack(buffer)\n\t\t\t\te.value = data[0]\n\t\t\t\te.string = data[1]\n\t\t\t\treturn e\n\t\t\t},\n\t\t\tpack: function(instance) {\n\t\t\t\treturn packr.pack([instance.value, instance.string])\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(data, deserialized)\n\t\tassert.equal(deserialized.extendedInstance.getDouble(), 8)\n\t})\n\n\ttest('extended Array class read/write', function(){\n\t\tvar instance = new ExtendArray()\n\t\tinstance.push(0);\n\t\tinstance.push(1);\n\t\tinstance.push(2);\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: ExtendArray,\n\t\t\ttype: 12,\n\t\t\tread: function(data) {\n\t\t\t\tObject.setPrototypeOf(data, ExtendArray.prototype)\n\t\t\t\treturn data\n\t\t\t},\n\t\t\twrite: function(instance) {\n\t\t\t\treturn [...instance]\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.strictEqual(Object.getPrototypeOf(deserialized.extendedInstance), ExtendArray.prototype)\n\t\tassert.deepEqual(data, deserialized)\n\t})\n\n\ttest('unregistered extended Array class read/write', function(){\n\t\tvar instance = new ExtendArray2()\n\t\tinstance.push(0);\n\t\tinstance.push(1);\n\t\tinstance.push(2);\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.strictEqual(Object.getPrototypeOf(deserialized.extendedInstance), Array.prototype)\n\t\tassert.deepEqual(data, deserialized)\n\t})\n\n\n\ttest('unregistered extended Object class read/write', function(){\n\t\tvar instance = new ExtendObject()\n\t\tinstance.test1 = \"string\";\n\t\tinstance.test2 = 3421321;\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.strictEqual(Object.getPrototypeOf(deserialized.extendedInstance), Object.prototype)\n\t\tassert.deepEqual(data, deserialized)\n\t})\n\n\ttest('extended class pack/unpack custom size', function(){\n\t\tfunction TestClass() {\n\n\t\t}\n\t\taddExtension({\n\t\t\tClass: TestClass,\n\t\t\ttype: 0x01,\n\t\t\tpack() {\n\t\t\t\treturn typeof Buffer != 'undefined' ? Buffer.alloc(256) : new Uint8Array(256)\n\t\t\t},\n\t\t\tunpack(data) {\n\t\t\t\treturn data.length\n\t\t\t}\n\t\t});\n\t\tlet result = unpack(pack(new TestClass()));\n\t\tassert.equal(result, 256)\n\t})\n\n\ttest('extended class read/write', function(){\n\t\tfunction Extended() {\n\n\t\t}\n\t\tExtended.prototype.getDouble = function() {\n\t\t\treturn this.value * 2\n\t\t}\n\t\tvar instance = new Extended()\n\t\tinstance.value = 4\n\t\tinstance.string = 'decode this: ᾜ'\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: Extended,\n\t\t\ttype: 12,\n\t\t\tread: function(data) {\n\t\t\t\tlet e = new Extended()\n\t\t\t\te.value = data[0]\n\t\t\t\te.string = data[1]\n\t\t\t\treturn e\n\t\t\t},\n\t\t\twrite: function(instance) {\n\t\t\t\treturn [instance.value, instance.string]\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(data, deserialized)\n\t\tassert.equal(deserialized.extendedInstance.getDouble(), 8)\n\t})\n\ttest('extended class return self', function(){\n\t\tfunction Extended() {\n\n\t\t}\n\t\tExtended.prototype.getDouble = function() {\n\t\t\treturn this.value * 2\n\t\t}\n\t\tvar instance = new Extended()\n\t\tinstance.value = 4\n\t\tinstance.string = 'decode this: ᾜ'\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: Extended,\n\t\t\ttype: 13,\n\t\t\tread: function(data) {\n\t\t\t\tObject.setPrototypeOf(data, Extended.prototype)\n\t\t\t\treturn data\n\t\t\t},\n\t\t\twrite: function(data) {\n\t\t\t\treturn data\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(data, deserialized)\n\t\tassert.strictEqual(Object.getPrototypeOf(deserialized.extendedInstance), Extended.prototype)\n\t\tassert.equal(deserialized.extendedInstance.getDouble(), 8)\n\t})\n\ttest('extended Array class return self', function(){\n\t\tvar instance = new ExtendArray3()\n\t\tinstance.push(0)\n\t\tinstance.push('has multi-byte: ᾜ')\n\t\tvar data = {\n\t\t\tprop1: 'has multi-byte: ᾜ',\n\t\t\textendedInstance: instance,\n\t\t\tprop2: 'more string',\n\t\t\tnum: 3,\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: ExtendArray3,\n\t\t\ttype: 14,\n\t\t\tread: function(data) {\n\t\t\t\tObject.setPrototypeOf(data, ExtendArray3.prototype)\n\t\t\t\treturn data\n\t\t\t},\n\t\t\twrite: function(data) {\n\t\t\t\treturn data\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(data, deserialized)\n\t\tassert.strictEqual(Object.getPrototypeOf(deserialized.extendedInstance), ExtendArray3.prototype)\n\t\tassert.equal(deserialized.extendedInstance[0], 0)\n\t})\n\n\ttest('extended class pack/unpack proxied', function(){\n\t\tfunction Extended() {\n\t\t\t\n\t\t}\n\t\tExtended.prototype.__call__ = function(){\n\t\t\treturn this.value * 4\n\t\t}\n\t\tExtended.prototype.getDouble = function() {\n\t\t\treturn this.value * 2\n\t\t}\n\n\t\tvar instance = function() { instance.__call__()/* callable stuff */ }\n\t\tObject.setPrototypeOf(instance,Extended.prototype);\n\t\t\n\t\tinstance.value = 4\n\t\tvar data = instance\n\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: Extended,\n\t\t\ttype: 15,\n\t\t\tunpack: function(buffer) {\n\t\t\t\tvar e = function() { e.__call__() }\n\t\t\t\tObject.setPrototypeOf(e,Extended.prototype);\n\t\t\t\te.value = packr.unpack(buffer)\n\t\t\t\treturn e\n\t\t\t},\n\t\t\tpack: function(instance) {\n\t\t\t\treturn packr.pack(instance.value)\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.equal(deserialized.getDouble(), 8)\n\t})\n\n\ttest.skip('convert Date to string', function(){\n\t\tvar data = {\n\t\t\taDate: new Date(),\n\t\t}\n\t\tlet packr = new Packr()\n\t\taddExtension({\n\t\t\tClass: Date,\n\t\t\twrite(date) {\n\t\t\t\treturn date.toString()\n\t\t\t}\n\t\t})\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.equal(deserialized.aDate, data.aDate.toString())\n\t})\n\ttest('standard pack fails on circular reference with shared structures', function () {\n\t\tvar data = {}\n\t\tdata.self = data;\n\t\tlet structures = []\n\t\tlet savedStructures\n\t\tlet packr = new Packr({\n\t\t\tstructures,\n\t\t\tsaveStructures(structures) {\n\t\t\t\tsavedStructures = structures\n\t\t\t}\n\t\t})\n\t\tassert.throws(function () {\n\t\t\tpackr.pack(data);\n\t\t});\n\t})\n\n\ttest('proto handling', function() {\n\t\tvar objectWithProto = JSON.parse('{\"__proto__\":{\"foo\":3}}');\n\t\tvar decoded = unpack(pack(objectWithProto));\n\t\tassert(!decoded.foo);\n\t\tvar objectsWithProto = [objectWithProto, objectWithProto, objectWithProto, objectWithProto, objectWithProto, objectWithProto];\n\t\tlet packr = new Packr();\n\t\tvar decoded = packr.unpack(packr.pack(objectsWithProto));\n\t\tfor (let object of decoded) {\n\t\t\tassert(!decoded.foo);\n\t\t}\n\t});\n\n\ttest.skip('text decoder', function() {\n\t\t\tlet td = new TextDecoder('ISO-8859-15')\n\t\t\tlet b = Buffer.alloc(3)\n\t\t\tlet total = 0\n\t\t\tfor (var i = 0; i < 256; i++) {\n\t\t\t\tb[0] = i\n\t\t\t\tb[1] = 0\n\t\t\t\tb[2] = 0\n\t\t\t\tlet s = td.decode(b)\n\t\t\t\tif (!require('msgpackr-extract').isOneByte(s)) {\n\t\t\t\t\tconsole.log(i.toString(16), s.length)\n\t\t\t\t\ttotal++\n\t\t\t\t}\n\t\t\t}\n\t})\n\n\ttest('moreTypes: Error with causes', function() {\n\t\tconst object = {\n\t\t\terror: new Error('test'),\n\t\t\terrorWithCause: new Error('test-1', { cause: new Error('test-2')}),\n\t\t}\n\t\tconst packr = new Packr({\n\t\t\tmoreTypes: true,\n\t\t})\n\n\t\tconst serialized = packr.pack(object)\n\t\tconst deserialized = packr.unpack(serialized)\n\t\tassert.equal(deserialized.error.message, object.error.message)\n\t\tassert.equal(deserialized.error.cause, object.error.cause)\n\t\tassert.equal(deserialized.errorWithCause.message, object.errorWithCause.message)\n\t\tassert.equal(deserialized.errorWithCause.cause.message, object.errorWithCause.cause.message)\n\t\tassert.equal(deserialized.errorWithCause.cause.cause, object.errorWithCause.cause.cause)\n\t})\n\n\ttest('structured cloning: self reference', function() {\n\t\tlet object = {\n\t\t\ttest: 'string',\n\t\t\tchildren: [\n\t\t\t\t{ name: 'child' }\n\t\t\t],\n\t\t\tvalue: new ArrayBuffer(10)\n\t\t}\n\t\tlet u8 = new Uint8Array(object.value)\n\t\tu8[0] = 1\n\t\tu8[1] = 2\n\t\tobject.self = object\n\t\tobject.children[1] = object\n\t\tobject.children[2] = object.children[0]\n\t\tobject.childrenAgain = object.children\n\t\tlet packr = new Packr({\n\t\t\tmoreTypes: true,\n\t\t\tstructuredClone: true,\n\t\t})\n\t\tvar serialized = packr.pack(object)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.equal(deserialized.self, deserialized)\n\t\tassert.equal(deserialized.children[0].name, 'child')\n\t\tassert.equal(deserialized.children[1], deserialized)\n\t\tassert.equal(deserialized.children[0], deserialized.children[2])\n\t\tassert.equal(deserialized.children, deserialized.childrenAgain)\n\t\tassert.equal(deserialized.value.constructor.name, 'ArrayBuffer')\n\t\tu8 = new Uint8Array(deserialized.value)\n\t\tassert.equal(u8[0], 1)\n\t\tassert.equal(u8[1], 2)\n\t})\n\n\ttest('structured cloning: self reference with more types', function() {\n\t\tlet set = new Set()\n\t\tset.add(['hello', 1, 2, { map: new Map([[set, set], ['a', 'b']]) }])\n\n\t\tlet packr = new Packr({\n\t\t\tmoreTypes: true,\n\t\t\tstructuredClone: true,\n\t\t})\n\t\tlet serialized = packr.pack(set)\n\t\tlet deserialized = packr.unpack(serialized)\n\t\tassert.equal(deserialized.constructor.name, 'Set')\n\t\tlet map = Array.from(deserialized)[0][3].map\n\t\tassert.equal(map.get(deserialized), deserialized)\n\n\t\tlet sizeTestMap = new Map()\n\t\tfor (let i = 0; i < 50; i++) {\n\t\t\tsizeTestMap.set(i || sizeTestMap, sizeTestMap)\n\t\t\tlet deserialized = packr.unpack(packr.pack(sizeTestMap))\n\t\t\tassert.equal(deserialized.size, i + 1)\n\t\t\tassert(deserialized.has(deserialized))\n\t\t\tassert(deserialized.has(i || deserialized))\n\t\t}\n\n\t\tlet sizeTestSet = new Set()\n\t\tfor (let i = 0; i < 50; i++) {\n\t\t\tsizeTestSet.add(i || sizeTestSet)\n\t\t\tlet deserialized = packr.unpack(packr.pack(sizeTestSet))\n\t\t\tassert.equal(deserialized.size, i + 1)\n\t\t\tassert(deserialized.has(deserialized))\n\t\t\tassert(deserialized.has(i || deserialized))\n\t\t}\n\t})\n\n\ttest('structured cloning: types', function() {\n\t\tlet b = typeof Buffer != 'undefined' ? Buffer.alloc(20) : new Uint8Array(20)\n\t\tlet fa = new Float32Array(b.buffer, 8, 2)\n\t\tfa[0] = 2.25\n\t\tfa[1] = 6\n\t\tlet object = {\n\t\t\terror: new Error('test'),\n\t\t\tset: new Set(['a', 'b']),\n\t\t\tregexp: /test/gi,\n\t\t\tfloat32Array: fa,\n\t\t\tuint16Array: new Uint16Array([3, 4]),\n\t\t\tarrayBuffer: new Uint8Array([0xde, 0xad]).buffer,\n\t\t\tdataView: new DataView(new Uint8Array([0xbe, 0xef]).buffer),\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tmoreTypes: true,\n\t\t\tstructuredClone: true,\n\t\t})\n\t\tvar serialized = packr.pack(object)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(Array.from(deserialized.set), Array.from(object.set))\n\t\tassert.equal(deserialized.error.message, object.error.message)\n\t\tassert.equal(deserialized.regexp.test('TEST'), true)\n\t\tassert.equal(deserialized.float32Array.constructor.name, 'Float32Array')\n\t\tassert.equal(deserialized.float32Array[0], 2.25)\n\t\tassert.equal(deserialized.float32Array[1], 6)\n\t\tassert.equal(deserialized.uint16Array.constructor.name, 'Uint16Array')\n\t\tassert.equal(deserialized.uint16Array[0], 3)\n\t\tassert.equal(deserialized.uint16Array[1], 4)\n\t\tassert.equal(deserialized.arrayBuffer.constructor.name, 'ArrayBuffer')\n\t\tassert.equal(new DataView(deserialized.arrayBuffer).getUint16(), 0xdead)\n\t\tassert.equal(deserialized.dataView.constructor.name, 'DataView')\n\t\tassert.equal(deserialized.dataView.getUint16(), 0xbeef)\n\t})\n\ttest('big bundledStrings', function() {\n\t\tconst MSGPACK_OPTIONS = {bundleStrings: true}\n\t\tconst packer = new Packr(MSGPACK_OPTIONS)\n\t\tconst unpacker = new Unpackr(MSGPACK_OPTIONS)\n\n\t\tconst payload = {\n\t\t\toutput: [\n\t\t\t\t{\n\t\t\t\t\turl: 'https://www.example.com/',\n\t\t\t\t},\n\t\t\t],\n\t\t}\n\n\t\tfor (let i = 0; i < 10000; i++) {\n\t\t\tpayload.output.push(payload.output[0])\n\t\t}\n\t\tlet deserialized = unpacker.unpack(packer.pack(payload));\n\t\tassert.equal(deserialized.output[0].url, payload.output[0].url);\n\t})\n\ttest('structured clone with bundled strings', function() {\n\t\tconst packer = new Packr({\n\t\t\tstructuredClone: true, // both options must be enabled\n\t\t\tbundleStrings: true,\n\t\t});\n\n\t\tconst v = {};\n\n\t\tlet shared = {\n\t\t\tname1: v,\n\t\t\tname2: v,\n\t\t};\n\n\t\tlet deserialized = packer.unpack(packer.pack(shared));\n\t\tassert.equal(deserialized.name1, deserialized.name2);\n\n\t\tshared = {};\n\t\tshared.aaaa = shared; // key length >= 4\n\n\t\tdeserialized = packer.unpack(packer.pack(shared));\n\t\tassert.equal(deserialized.aaaa, deserialized);\n\t})\n\n\ttest('object without prototype', function(){\n\t\tvar data = Object.create(null)\n\t\tdata.test = 3\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\n\ttest('object with __proto__', function(){\n\t\tconst data = { foo: 'bar', __proto__: { isAdmin: true } };\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, { foo: 'bar' });\n\t})\n\n\ttest('separate instances', function() {\n\t\tconst packr = new Packr({\n\t\t\tstructures: [['m', 'e'], ['action', 'share']]\n\t\t});\n\t\tconst packr2 = new Packr({\n\t\t\tstructures: [['m', 'e'], ['action', 'share']]\n\t\t});\n\t\tlet packed = packr.pack([{m: 1, e: 2}, {action: 3, share: 4}]);\n\t\t// also tried directly decoding this without the first Packr instance packed = new Uint8Array([0x92, 0x40, 0x01, 0x02, 0x41, 0x03, 0x04]);\n\t\tconsole.log(packr2.unpack(packed));\n\t})\n\n\ttest('many shared structures', function() {\n\t\tlet data = []\n\t\tfor (let i = 0; i < 200; i++) {\n\t\t\tdata.push({['a' + i]: i})\n\t\t}\n\t\tlet structures = []\n\t\tlet savedStructures\n\t\tlet packr = new Packr({\n\t\t\tstructures,\n\t\t\tsaveStructures(structures) {\n\t\t\t\tsavedStructures = structures\n\t\t\t}\n\t\t})\n\t\tvar serializedWith32 = packr.pack(data)\n\t\tassert.equal(savedStructures.length, 32)\n\t\tvar deserialized = packr.unpack(serializedWith32)\n\t\tassert.deepEqual(deserialized, data)\n\t\tstructures = structures.slice(0, 32)\n\t\tpackr = new Packr({\n\t\t\tstructures,\n\t\t\tmaxSharedStructures: 100,\n\t\t\tsaveStructures(structures) {\n\t\t\t\tsavedStructures = structures\n\t\t\t}\n\t\t})\n\t\tdeserialized = packr.unpack(serializedWith32)\n\t\tassert.deepEqual(deserialized, data)\n\t\tstructures = structures.slice(0, 32)\n\t\tpackr = new Packr({\n\t\t\tstructures,\n\t\t\tmaxSharedStructures: 100,\n\t\t\tsaveStructures(structures) {\n\t\t\t\tsavedStructures = structures\n\t\t\t}\n\t\t})\n\t\tlet serialized = packr.pack(data)\n\t\tassert.equal(savedStructures.length, 100)\n\t\tdeserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\n\t\tdeserialized = packr.unpack(serializedWith32)\n\t\tassert.deepEqual(deserialized, data)\n\t\tassert.equal(savedStructures.length, 100)\n\n\t\tdeserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tassert.equal(packr.structures.sharedLength, 100)\n\t})\n\ttest('more shared structures', function() {\n\t\tconst structures = []\n\t\tfor (let i = 0; i < 40; i++) {\n\t\t\tstructures.push(['a' + i])\n\t\t}\n\t\tconst structures2 = [...structures]\n\t\tconst packr = new Packr({\n\t\t\tgetStructures() {\n\t\t\t\treturn structures\n\t\t\t},\n\t\t\tsaveStructures(structures) {\t\t  \n\t\t\t},\n\t\t\tmaxSharedStructures: 100\n\t\t})\n\t\tconst packr2 = new Packr({\n\t\t\tgetStructures() {\n\t\t\t\treturn structures2\n\t\t\t},\n\t\t\tsaveStructures(structures) {\t\t  \n\t\t\t},\n\t\t\tmaxSharedStructures: 100\n\t\t})\n\t\tconst inputData = {a35: 35}\n\t\tconst buffer = packr.pack(inputData)\n\t\tconst outputData = packr2.decode(buffer)\n\t\tassert.deepEqual(inputData, outputData)\n\t})\n\n\ttest('big buffer', function() {\n\t\tvar size = 100000000\n\t\tvar data = new Uint8Array(size).fill(1)\n\t\tvar packed = pack(data)\n\t\tvar unpacked = unpack(packed)\n\t\tassert.equal(unpacked.length, size)\n\t})\n\n\ttest('random strings', function(){\n\t\tvar data = []\n\t\tfor (var i = 0; i < 2000; i++) {\n\t\t\tvar str = 'test'\n\t\t\twhile (Math.random() < 0.7 && str.length < 0x100000) {\n\t\t\t\tstr = str + String.fromCharCode(90/(Math.random() + 0.01)) + str\n\t\t\t}\n\t\t\tdata.push(str)\n\t\t}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\n\ttest('map/date', function(){\n\t\tvar map = new Map()\n\t\tmap.set(4, 'four')\n\t\tmap.set('three', 3)\n\n\n\t\tvar data = {\n\t\t\tmap: map,\n\t\t\tdate: new Date(1532219539733),\n\t\t\tfarFutureDate: new Date(3532219539133),\n\t\t\tfartherFutureDate: new Date('2106-08-05T18:48:20.323Z'),\n\t\t\tancient: new Date(-3532219539133),\n\t\t\tinvalidDate: new Date('invalid')\n\t\t}\n\t\tlet packr = new Packr()\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.equal(deserialized.map.get(4), 'four')\n\t\tassert.equal(deserialized.map.get('three'), 3)\n\t\tassert.equal(deserialized.date.getTime(), 1532219539733)\n\t\tassert.equal(deserialized.farFutureDate.getTime(), 3532219539133)\n\t\tassert.equal(deserialized.fartherFutureDate.toISOString(), '2106-08-05T18:48:20.323Z')\n\t\tassert.equal(deserialized.ancient.getTime(), -3532219539133)\n\t\tassert.equal(deserialized.invalidDate.toString(), 'Invalid Date')\n\t})\n\ttest('map/date with options', function(){\n\t\tvar map = new Map()\n\t\tmap.set(4, 'four')\n\t\tmap.set('three', 3)\n\n\n\t\tvar data = {\n\t\t\tmap: map,\n\t\t\tdate: new Date(1532219539011),\n\t\t\tinvalidDate: new Date('invalid')\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tmapsAsObjects: true,\n\t\t\tuseTimestamp32: true,\n\t\t\tonInvalidDate: () => 'Custom invalid date'\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.equal(deserialized.map[4], 'four')\n\t\tassert.equal(deserialized.map.three, 3)\n\t\tassert.equal(deserialized.date.getTime(), 1532219539000)\n\t\tassert.equal(deserialized.invalidDate, 'Custom invalid date')\n\t})\n\ttest('key caching', function() {\n\t\tvar data = {\n\t\t\tfoo: 2,\n\t\t\tbar: 'test',\n\t\t\tfour: 4,\n\t\t\tseven: 7,\n\t\t\tfoz: 3,\n\t\t}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\t// do multiple times to test caching\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('strings', function() {\n\t\tvar data = ['']\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\t// do multiple times\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tdata = 'decode this: ᾜ'\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tdata = 'decode this that is longer but without any non-latin characters'\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('decimal float32', function() {\n\t\tvar data = {\n\t\t\ta: 2.526,\n\t\t\tb: 0.0035235,\n\t\t\tc: 0.00000000000352501,\n\t\t\td: 3252.77,\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tuseFloat32: DECIMAL_FIT\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tassert.equal(serialized.length, 32)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('int64/uint64 should be bigints by default', function() {\n\t\tvar data = {\n\t\t\ta: 325283295382932843n\n\t\t}\n\n\t\tlet packr = new Packr()\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized.a, 325283295382932843n)\n\t})\n\ttest('bigint to float', function() {\n\t\tvar data = {\n\t\t\ta: 325283295382932843n\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tint64AsType: 'number'\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized.a, 325283295382932843)\n\t})\n\ttest('int64AsNumber compatibility', function() {\n\t\t// https://github.com/kriszyp/msgpackr/pull/85\n\t\tvar data = {\n\t\t\ta: 325283295382932843n\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tint64AsNumber: true\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized.a, 325283295382932843)\n\t})\n\ttest('bigint to auto (float or bigint)', function() {\n\t\tvar data = {\n\t\t\ta: -9007199254740993n,\n\t\t\tb: -9007199254740992n,\n\t\t\tc: 0n,\n\t\t\td: 9007199254740992n,\n\t\t\te: 9007199254740993n,\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tint64AsType: 'auto'\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized.a, -9007199254740993n)\n\t\tassert.deepEqual(deserialized.b, -9007199254740992)\n\t\tassert.deepEqual(deserialized.c, 0)\n\t\tassert.deepEqual(deserialized.d, 9007199254740992)\n\t\tassert.deepEqual(deserialized.e, 9007199254740993n)\n\t})\n\ttest('bigint to string', function() {\n\t\tvar data = {\n\t\t\ta: 325283295382932843n,\n\t\t}\n\t\tlet packr = new Packr({\n\t\t\tint64AsType: 'string'\n\t\t})\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized.a, '325283295382932843')\n\t})\n\ttest('fixint should be one byte', function(){\n\t\tlet encoded = pack(123);\n\t\tassert.equal(encoded.length, 1);\n\t});\n\ttest('numbers', function(){\n\t\tvar data = {\n\t\t\tbigEncodable: 48978578104322,\n\t\t\tdateEpoch: 1530886513200,\n\t\t\trealBig: 3432235352353255323,\n\t\t\tdecimal: 32.55234,\n\t\t\tnegative: -34.11,\n\t\t\texponential: 0.234e123,\n\t\t\ttiny: 3.233e-120,\n\t\t\tzero: 0,\n\t\t\t//negativeZero: -0,\n\t\t\tInfinity: Infinity\n\t\t}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('bigint', function(){\n\t\tvar data = {\n\t\t\tbigintSmall: 352n,\n\t\t\tbigintSmallNegative: -333335252n,\n\t\t\tbigintBig: 2n**64n - 1n, // biggest possible\n\t\t\tbigintBigNegative: -(2n**63n), // largest negative\n\t\t\tmixedWithNormal: 44,\n\t\t}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t\tvar tooBigInt = {\n\t\t\ttooBig: 2n**66n\n\t\t}\n\t\tassert.throws(function(){ serialized = pack(tooBigInt) })\n\t\tlet packr = new Packr({\n\t\t\tlargeBigIntToFloat: true\n\t\t})\n\t\tserialized = packr.pack(tooBigInt)\n\t\tdeserialized = unpack(serialized)\n\t\tassert.isTrue(deserialized.tooBig > 2n**65n)\n\n\t\tpackr = new Packr({\n\t\t\tlargeBigIntToString: true\n\t\t})\n\t\tserialized = packr.pack(tooBigInt)\n\t\tdeserialized = unpack(serialized)\n\t\tassert.equal(deserialized.tooBig, (2n**66n).toString())\n\t})\n\n\ttest('roundFloat32', function() {\n\t\tassert.equal(roundFloat32(0.00333000003), 0.00333)\n\t\tassert.equal(roundFloat32(43.29999999993), 43.3)\n\t})\n\n\ttest('buffers', function(){\n\t\tvar data = {\n\t\t\tbuffer1: new Uint8Array([2,3,4]),\n\t\t\tbuffer2: new Uint8Array(pack(sampleData))\n\t\t}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\n\ttest('notepack test', function() {\n\t\tconst data = {\n\t\t  foo: 1,\n\t\t  bar: [1, 2, 3, 4, 'abc', 'def'],\n\t\t  foobar: {\n\t\t    foo: true,\n\t\t    bar: -2147483649,\n\t\t    foobar: {\n\t\t      foo: new Uint8Array([1, 2, 3, 4, 5]),\n\t\t      bar: 1.5,\n\t\t      foobar: [true, false, 'abcdefghijkmonpqrstuvwxyz']\n\t\t    }\n\t\t  }\n\t\t};\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tvar deserialized = unpack(serialized)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\n\ttest('arrays in map keys', function() {\n\t\tconst msgpackr = new Packr({ mapsAsObjects: true, allowArraysInMapKeys: true });\n\n\t\tconst map = new Map();\n\t\tmap.set([1, 2, 3], 1);\n\t\tmap.set([1, 2, ['foo', 3.14]], 2);\n\n\t\tconst packed = msgpackr.pack(map);\n\t\tconst unpacked = msgpackr.unpack(packed);\n\t\tassert.deepEqual(unpacked, { '1,2,3': 1, '1,2,foo,3.14': 2 });\n\t})\n\n\ttest('utf16 causing expansion', function() {\n\t\tthis.timeout(10000)\n\t\tlet data = {fixstr: 'ᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝ', str8:'ᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝᾐᾑᾒᾓᾔᾕᾖᾗᾘᾙᾚᾛᾜᾝ'}\n\t\tvar serialized = pack(data)\n\t\tvar deserialized = unpack(serialized)\n\t\tassert.deepEqual(deserialized, data)\n\t})\n\ttest('unpackMultiple', () => {\n\t\tlet values = unpackMultiple(new Uint8Array([1, 2, 3, 4]))\n\t\tassert.deepEqual(values, [1, 2, 3, 4])\n\t\tvalues = []\n\t\tunpackMultiple(new Uint8Array([1, 2, 3, 4]), value => values.push(value))\n\t\tassert.deepEqual(values, [1, 2, 3, 4])\n\t})\n\n\ttest('unpackMultiple with positions', () => {\n\t\tlet values = unpackMultiple(new Uint8Array([1, 2, 3, 4]))\n\t\tassert.deepEqual(values, [1, 2, 3, 4])\n\t\tvalues = []\n\t\tunpackMultiple(new Uint8Array([1, 2, 3, 4]), (value,start,end) => values.push([value,start,end]))\n\t\tassert.deepEqual(values, [[1,0,1], [2,1,2], [3,2,3], [4,3,4]])\n\t})\n\n\ttest('pack toJSON returning this', () => {\n\t\tclass Serializable {\n\t\t\tsomeData = [1, 2, 3, 4]\n\t\t\ttoJSON() {\n\t\t\t\treturn this\n\t\t\t}\n\t\t}\n\t\tconst serialized = pack(new Serializable)\n\t\tconst deserialized = unpack(serialized)\n\t\tassert.deepStrictEqual(deserialized, { someData: [1, 2, 3, 4] })\n\t})\n\ttest('skip values', function () {\n\t\tvar data = {\n\t\t\tdata: [\n\t\t\t\t{ a: 1, name: 'one', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 2, name: 'two', type: 'even', isOdd: undefined },\n\t\t\t\t{ a: 3, name: 'three', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 4, name: 'four', type: 'even', isOdd: null},\n\t\t\t\t{ a: 5, name: 'five', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 6, name: 'six', type: 'even', isOdd: null }\n\t\t\t],\n\t\t\tdescription: 'some names',\n\t\t\ttypes: ['odd', 'even'],\n\t\t\tconvertEnumToNum: [\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 1 },\n\t\t\t\t{ prop: 2 },\n\t\t\t\t{ prop: [undefined, null] },\n\t\t\t\t{ prop: null }\n\t\t\t]\n\t\t}\n\t\tvar expected = {\n\t\t\tdata: [\n\t\t\t\t{ a: 1, name: 'one', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 2, name: 'two', type: 'even' },\n\t\t\t\t{ a: 3, name: 'three', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 4, name: 'four', type: 'even', },\n\t\t\t\t{ a: 5, name: 'five', type: 'odd', isOdd: true },\n\t\t\t\t{ a: 6, name: 'six', type: 'even' }\n\t\t\t],\n\t\t\tdescription: 'some names',\n\t\t\ttypes: ['odd', 'even'],\n\t\t\tconvertEnumToNum: [\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 'test' },\n\t\t\t\t{ prop: 1 },\n\t\t\t\t{ prop: 2 },\n\t\t\t\t{ prop: [undefined, null] },\n\t\t\t\t{}\n\t\t\t]\n\t\t}\n\t\tlet packr = new Packr({ useRecords: false, skipValues: [undefined, null] })\n\t\tvar serialized = packr.pack(data)\n\t\tvar deserialized = packr.unpack(serialized)\n\t\tassert.deepEqual(deserialized, expected)\n\t})\n})\nsuite('msgpackr performance tests', function(){\n\ttest('performance JSON.parse', function() {\n\t\tvar data = sampleData\n\t\tthis.timeout(10000)\n\t\tlet structures = []\n\t\tvar serialized = JSON.stringify(data)\n\t\tconsole.log('JSON size', serialized.length)\n\t\tfor (var i = 0; i < ITERATIONS; i++) {\n\t\t\tvar deserialized = JSON.parse(serialized)\n\t\t}\n\t})\n\ttest('performance JSON.stringify', function() {\n\t\tvar data = sampleData\n\t\tthis.timeout(10000)\n\t\tfor (var i = 0; i < ITERATIONS; i++) {\n\t\t\tvar serialized = JSON.stringify(data)\n\t\t}\n\t})\n\ttest('performance unpack', function() {\n\t\tvar data = sampleData\n\t\tthis.timeout(10000)\n\t\tlet structures = []\n\t\tvar serialized = pack(data)\n\t\tconsole.log('MessagePack size', serialized.length)\n\t\tlet packr = new Packr({ structures, bundleStrings: false })\n\t\tvar serialized = packr.pack(data)\n\t\tconsole.log('msgpackr w/ record ext size', serialized.length)\n\t\tfor (var i = 0; i < ITERATIONS; i++) {\n\t\t\tvar deserialized = packr.unpack(serialized)\n\t\t}\n\t})\n\ttest('performance pack', function() {\n\t\tvar data = sampleData\n\t\tthis.timeout(10000)\n\t\tlet structures = []\n\t\tlet packr = new Packr({ structures, bundleStrings: false })\n\t\tlet buffer = typeof Buffer != 'undefined' ? Buffer.alloc(0x10000) : new Uint8Array(0x10000)\n\n\t\tfor (var i = 0; i < ITERATIONS; i++) {\n\t\t\t//serialized = pack(data, { shared: sharedStructure })\n\t\t\tpackr.useBuffer(buffer)\n\t\t\tvar serialized = packr.pack(data)\n\t\t\t//var serializedGzip = deflateSync(serialized)\n\t\t}\n\t\t//console.log('serialized', serialized.length, global.propertyComparisons)\n\t})\n})\n"], "names": ["position", "bundledStrings", "readStruct", "onLoadedStructures", "saveState", "Unpackr", "addExtension", "unpack", "unpackMultiple", "roundFloat32", "textEncoder", "hasNodeBuffer", "prepareStructures", "unpackAddExtension", "<PERSON><PERSON>", "pack", "createRequire", "readFileSync", "msgpackr.Packr", "msgpackr.Unpackr", "msgpackr.unpack", "msgpackr.unpackMultiple", "msgpackr.roundFloat32", "msgpackr.pack", "msgpackr.FLOAT32_OPTIONS", "msgpackr.addExtension"], "mappings": ";;;CAAA,IAAI,QAAO;CACX,IAAI;CACJ,CAAC,OAAO,GAAG,IAAI,WAAW,GAAE;CAC5B,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;CACjB,IAAI,IAAG;CACP,IAAI,OAAM;CACV,IAAIA,UAAQ,GAAG,EAAC;CAEhB,MAAM,WAAW,GAAG,GAAE;CACtB,IAAI,OAAO,GAAG,YAAW;CACzB,IAAI,cAAc,GAAG,EAAC;CACtB,IAAI,cAAc,GAAG,GAAE;CACvB,IAAI,kBAAiB;CACrB,IAAI,UAAS;CACb,IAAI,cAAc,GAAG,EAAC;CACtB,IAAI,YAAY,GAAG,EAAC;CACpB,IAAIC,iBAAc;CAClB,IAAI,aAAY;CAChB,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI,SAAQ;CACZ,IAAI,cAAc,GAAG;CACrB,CAAC,UAAU,EAAE,KAAK;CAClB,CAAC,aAAa,EAAE,IAAI;CACpB,EAAC;CACM,MAAM,MAAM,CAAC,EAAE;CACf,MAAM,EAAE,GAAG,IAAI,MAAM,GAAE;CAC9B,EAAE,CAAC,IAAI,GAAG,mBAAkB;CAC5B,IAAI,cAAc,GAAG,MAAK;CAC1B,IAAI,yBAAyB,GAAG,EAAC;CACjC,IAAIC,YAAU,EAAEC,oBAAkB,EAAE,YAAW;CAE/C;CACA,IAAI;CACJ,CAAC,IAAI,QAAQ,CAAC,EAAE,EAAC;CACjB,CAAC,CAAC,MAAM,KAAK,EAAE;CACf;CACA,CAAC,yBAAyB,GAAG,SAAQ;CACrC,CAAC;AACD;iBACO,MAAM,OAAO,CAAC;CACrB,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,IAAI,OAAO,EAAE;CACf,GAAG,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;CAC1E,IAAI,OAAO,CAAC,aAAa,GAAG,KAAI;CAChC,GAAG,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;CACxD,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;CAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE;CAC5D,KAAK,OAAO,CAAC,UAAU,GAAG,GAAE;CAC5B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB;CACrC,MAAM,OAAO,CAAC,mBAAmB,GAAG,EAAC;CACrC,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,OAAO,CAAC,UAAU;CACzB,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,OAAM;CAC/D,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;CACnC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,aAAa,GAAG,KAAI;CAClD,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,EAAC;CACvC,IAAI;CACJ,GAAG,IAAI,OAAO,CAAC,aAAa,EAAE;CAC9B,IAAI,OAAO,CAAC,WAAW,GAAG,SAAQ;CAClC,IAAI;CACJ,GAAG;CACH,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAC;CAC9B,EAAE;CACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;CACzB,EAAE,IAAI,GAAG,EAAE;CACX;CACA,GAAG,OAAOC,WAAS,CAAC,MAAM;CAC1B,IAAI,WAAW,GAAE;CACjB,IAAI,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAGC,SAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;CAC/G,IAAI,CAAC;CACL,GAAG;CACH,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW;CAC1D,GAAG,MAAM,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;CACzF,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;CACnC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,OAAM;CACxC,GAAGL,UAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,EAAC;CAChC,GAAG,MAAM;CACT,GAAGA,UAAQ,GAAG,EAAC;CACf,GAAG,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,OAAM;CAClD,GAAG;CACH,EAAE,cAAc,GAAG,EAAC;CACpB,EAAE,YAAY,GAAG,EAAC;CAClB,EAAE,SAAS,GAAG,KAAI;CAClB,EAAE,OAAO,GAAG,YAAW;CACvB,EAAEC,gBAAc,GAAG,KAAI;CACvB,EAAE,GAAG,GAAG,OAAM;CACd;CACA;CACA;CACA,EAAE,IAAI;CACN,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;CACtH,GAAG,CAAC,MAAM,KAAK,EAAE;CACjB;CACA,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,MAAM,YAAY,UAAU;CACnC,IAAI,MAAM,KAAK;CACf,GAAG,MAAM,IAAI,KAAK,CAAC,kDAAkD,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC;CAC1J,GAAG;CACH,EAAE,IAAI,IAAI,YAAYI,SAAO,EAAE;CAC/B,GAAG,cAAc,GAAG,KAAI;CACxB,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACxB,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAU;CACvC,IAAI,OAAO,WAAW,CAAC,OAAO,CAAC;CAC/B,IAAI,MAAM,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;CAClE,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,cAAc,GAAG,eAAc;CAClC,GAAG,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;CACzD,IAAI,iBAAiB,GAAG,GAAE;CAC1B,GAAG;CACH,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC;CAC7B,EAAE;CACF,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;CACjC,EAAE,IAAI,MAAM,EAAE,YAAY,GAAG,EAAC;CAC9B,EAAE,IAAI;CACN,GAAG,cAAc,GAAG,KAAI;CACxB,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,OAAM;CAC3B,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAC;CACrF,GAAG,IAAI,OAAO,EAAE;CAChB,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,YAAY,EAAEL,UAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;CACjE,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;CAC3B,KAAK,YAAY,GAAGA,WAAQ;CAC5B,KAAK,IAAI,OAAO,CAAC,WAAW,EAAE,EAAE,YAAY,EAAEA,UAAQ,CAAC,KAAK,KAAK,EAAE;CACnE,MAAM,MAAM;CACZ,MAAM;CACN,KAAK;CACL,IAAI;CACJ,QAAQ;CACR,IAAI,MAAM,GAAG,EAAE,KAAK,GAAE;CACtB,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;CAC3B,KAAK,YAAY,GAAGA,WAAQ;CAC5B,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC;CAC/B,KAAK;CACL,IAAI,OAAO,MAAM;CACjB,IAAI;CACJ,GAAG,CAAC,MAAM,KAAK,EAAE;CACjB,GAAG,KAAK,CAAC,YAAY,GAAG,aAAY;CACpC,GAAG,KAAK,CAAC,MAAM,GAAG,OAAM;CACxB,GAAG,MAAM,KAAK;CACd,GAAG,SAAS;CACZ,GAAG,cAAc,GAAG,MAAK;CACzB,GAAG,WAAW,GAAE;CAChB,GAAG;CACH,EAAE;CACF,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;CACxD,EAAE,IAAIG,oBAAkB;CACxB,GAAG,gBAAgB,GAAGA,oBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;CACtE,EAAE,gBAAgB,GAAG,gBAAgB,IAAI,GAAE;CAC3C,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;CACvC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;CAC3E,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC3D,GAAG,IAAI,SAAS,GAAG,gBAAgB,CAAC,CAAC,EAAC;CACtC,GAAG,IAAI,SAAS,EAAE;CAClB,IAAI,SAAS,CAAC,QAAQ,GAAG,KAAI;CAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;CACf,KAAK,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAC;CACvC,IAAI;CACJ,GAAG;CACH,EAAE,gBAAgB,CAAC,YAAY,GAAG,gBAAgB,CAAC,OAAM;CACzD,EAAE,KAAK,IAAI,EAAE,IAAI,kBAAkB,IAAI,EAAE,EAAE;CAC3C,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE;CAChB,IAAI,IAAI,SAAS,GAAG,gBAAgB,CAAC,EAAE,EAAC;CACxC,IAAI,IAAI,QAAQ,GAAG,kBAAkB,CAAC,EAAE,EAAC;CACzC,IAAI,IAAI,QAAQ,EAAE;CAClB,KAAK,IAAI,SAAS;CAClB,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,UAAS;CACvG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG,SAAQ;CACpC,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE,OAAO,IAAI,CAAC,UAAU,GAAG,gBAAgB;CAC3C,EAAE;CACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;CACzB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;CACrC,EAAE;CACF,EAAC;CAIM,SAAS,WAAW,CAAC,OAAO,EAAE;CACrC,CAAC,IAAI;CACL,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;CAClD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,IAAI,EAAC;CACzD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,MAAM;CAC9C,IAAI,iBAAiB,CAAC,MAAM,GAAG,aAAY;CAC3C,GAAG;CACH,EAAE,IAAI,OAAM;CACZ,EAAE,IAAI,cAAc,CAAC,qBAAqB,IAAI,GAAG,CAACH,UAAQ,CAAC,GAAG,IAAI,IAAI,GAAG,CAACA,UAAQ,CAAC,IAAI,IAAI,IAAIE,YAAU,EAAE;CAC3G,GAAG,MAAM,GAAGA,YAAU,CAAC,GAAG,EAAEF,UAAQ,EAAE,MAAM,EAAE,cAAc,EAAC;CAC7D,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;CAC3C,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,GAAE;CAC5B,GAAGA,UAAQ,GAAG,OAAM;CACpB,GAAG;CACH,GAAG,MAAM,GAAG,IAAI,GAAE;CAClB,EAAE,IAAIC,gBAAc,EAAE;CACtB,GAAGD,UAAQ,GAAGC,gBAAc,CAAC,mBAAkB;CAC/C,GAAGA,gBAAc,GAAG,KAAI;CACxB,GAAG;CACH,EAAE,IAAI,cAAc;CACpB;CACA;CACA,GAAG,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;AAC7C;CACA,EAAE,IAAID,UAAQ,IAAI,MAAM,EAAE;CAC1B;CACA,GAAG,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;CAC/D,IAAI,iBAAiB,GAAE;CACvB,GAAG,iBAAiB,GAAG,KAAI;CAC3B,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,YAAY;CACnB,IAAI,YAAY,GAAG,KAAI;CACvB,GAAG,MAAM,IAAIA,UAAQ,GAAG,MAAM,EAAE;CAChC;CACA,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;CACxD,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;CAC9B,GAAG,IAAI,QAAQ,CAAC;CAChB,GAAG,IAAI;CACP,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAC;CAClH,IAAI,CAAC,MAAM,KAAK,EAAE;CAClB,IAAI,QAAQ,GAAG,2BAA2B,GAAG,KAAK,GAAG,IAAG;CACxD,IAAI;CACJ,GAAG,MAAM,IAAI,KAAK,CAAC,2CAA2C,GAAG,QAAQ,CAAC;CAC1E,GAAG;CACH;CACA,EAAE,OAAO,MAAM;CACf,EAAE,CAAC,MAAM,KAAK,EAAE;CAChB,EAAE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;CAC9D,GAAG,iBAAiB,GAAE;CACtB,EAAE,WAAW,GAAE;CACf,EAAE,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAIA,UAAQ,GAAG,MAAM,EAAE;CAChH,GAAG,KAAK,CAAC,UAAU,GAAG,KAAI;CAC1B,GAAG;CACH,EAAE,MAAM,KAAK;CACb,EAAE;CACF,CAAC;AACD;CACA,SAAS,iBAAiB,GAAG;CAC7B,CAAC,KAAK,IAAI,EAAE,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;CACrD,EAAE,iBAAiB,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,EAAE,EAAC;CACjE,EAAE;CACF,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;CAC3C,CAAC;AACD;CACO,SAAS,IAAI,GAAG;CACvB,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;CACnB,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;CACpB,GAAG,IAAI,KAAK,GAAG,IAAI;CACnB,IAAI,OAAO,KAAK;CAChB,QAAQ;CACR,IAAI,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;CACnD,KAAK,cAAc,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC,KAAK,GAAG,IAAI,EAAC;CACnE,IAAI,IAAI,SAAS,EAAE;CACnB,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;CAC1B,MAAM,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI,EAAC;CACrE,MAAM;CACN,KAAK,OAAO,SAAS,CAAC,IAAI,EAAE;CAC5B,KAAK;CACL,KAAK,OAAO,KAAK;CACjB,IAAI;CACJ,GAAG,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;CAC3B;CACA,GAAG,KAAK,IAAI,KAAI;CAChB,GAAG,IAAI,cAAc,CAAC,aAAa,EAAE;CACrC,IAAI,IAAI,MAAM,GAAG,GAAE;CACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACpC,KAAK,IAAI,GAAG,GAAG,OAAO,GAAE;CACxB,KAAK,IAAI,GAAG,KAAK,WAAW;CAC5B,MAAM,GAAG,GAAG,WAAU;CACtB,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACzB,KAAK;CACL,IAAI,OAAO,MAAM;CACjB,IAAI,MAAM;CACV,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACpC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;CAC5B,KAAK;CACL,IAAI,OAAO,GAAG;CACd,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,KAAK,IAAI,KAAI;CAChB,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,EAAC;CAC/B,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACnC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;CACrB,IAAI;CACJ,GAAG,IAAI,cAAc,CAAC,UAAU;CAChC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;CAC/B,GAAG,OAAO,KAAK;CACf,GAAG;CACH,EAAE,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;CAC1B;CACA,EAAE,IAAI,MAAM,GAAG,KAAK,GAAG,KAAI;CAC3B,EAAE,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAChC,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;CAC3F,GAAG;CACH,EAAE,IAAI,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,EAAE;CACzC;CACA,GAAG,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;CAC9E,GAAG,IAAI,MAAM,IAAI,IAAI;CACrB,IAAI,OAAO,MAAM;CACjB,GAAG;CACH,EAAE,OAAO,eAAe,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM;CACR,EAAE,IAAI,MAAK;CACX,EAAE,QAAQ,KAAK;CACf,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;CACzB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAIC,gBAAc,EAAE;CACxB,KAAK,KAAK,GAAG,IAAI,GAAE;CACnB,KAAK,IAAI,KAAK,GAAG,CAAC;CAClB,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;CACjG;CACA,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;CACjG,KAAK;CACL,IAAI,OAAO,EAAE,CAAC;CACd,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;CAC1B,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACD,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,KAAK,KAAK,SAAS;CAC3B,KAAK,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;CAChD,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CACnC,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;CACzC,IAAI,IAAI,cAAc,CAAC,UAAU,GAAG,CAAC,EAAE;CACvC;CACA,KAAK,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAACA,UAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CACtF,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,OAAO,CAAC,CAAC,UAAU,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;CAC/E,KAAK;CACL,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB;CACA,GAAG,KAAK,IAAI;CACZ,IAAI,OAAO,GAAG,CAACA,UAAQ,EAAE,CAAC;CAC1B,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,YAAW;CACvD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;CACvD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;CACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;CAC5C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;CAC1D,KAAK;CACL,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;CAC5C,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;AAChB;CACA;CACA,GAAG,KAAK,IAAI;CACZ,IAAI,OAAO,QAAQ,CAAC,OAAO,CAACA,UAAQ,EAAE,CAAC;CACvC,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;CACvC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;CACvC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,CAAC,GAAG,YAAW;CACtD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;CACtD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;CACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;CAC3C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;CACzF,KAAK;CACL,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;CAC3C,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;AAChB;CACA,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;CACvB,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;CACpD,KAAK,MAAM;CACX,KAAK,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,EAAC;CAC7C,KAAK,IAAI,SAAS,EAAE;CACpB,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE;CAC1B,OAAOA,UAAQ,GAAE;CACjB,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACpC,OAAO,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE;CACrC,OAAOA,UAAQ,GAAE;CACjB,OAAO,OAAO,SAAS,EAAE;CACzB,OAAO;CACP,OAAO,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,EAAEA,UAAQ,CAAC,CAAC;CAC3D,MAAM;CACN,MAAM,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;CACnD,KAAK;CACL,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;CACzB,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;CACvB,KAAKA,UAAQ,GAAE;CACf,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CACrE,KAAK;CACL,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC;CACtB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;CACrB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;CACrB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC;CACtB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;CAC7B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;CAC9B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;CAC9B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;CAC3B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;CAC3B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG;CACH,IAAI,IAAI,KAAK,IAAI,IAAI;CACrB,KAAK,OAAO,KAAK,GAAG,KAAK;CACzB,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;CAC7B,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,oCAAoC,EAAC;CAChE,KAAK,KAAK,CAAC,UAAU,GAAG,KAAI;CAC5B,KAAK,MAAM,KAAK;CAChB,KAAK;CACL,IAAI,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,KAAK,CAAC;AACzD;CACA,GAAG;CACH,EAAE;CACF,CAAC;CACD,MAAM,SAAS,GAAG,4BAA2B;CAC7C,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;CACnD,CAAC,SAAS,UAAU,GAAG;CACvB;CACA,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE,GAAG,yBAAyB,EAAE;CACtD,GAAG,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,EAAE,2BAA2B,IAAI,cAAc,CAAC,UAAU,GAAG,eAAe,GAAG,EAAE,CAAC;CACxI,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,WAAW,GAAG,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAC;CAC5K,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC;CAC/B,IAAI,SAAS,CAAC,IAAI,GAAG,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,EAAC;CACpE,GAAG,OAAO,UAAU,EAAE;CACtB,GAAG;CACH,EAAE,IAAI,MAAM,GAAG,GAAE;CACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACpD,GAAG,IAAI,GAAG,GAAG,SAAS,CAAC,CAAC,EAAC;CACzB,GAAG,IAAI,GAAG,KAAK,WAAW;CAC1B,IAAI,GAAG,GAAG,WAAU;CACpB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACvB,GAAG;CACH,EAAE,IAAI,cAAc,CAAC,UAAU;CAC/B,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CAChC,EAAE,OAAO,MAAM;CACf,EAAE;CACF,CAAC,UAAU,CAAC,KAAK,GAAG,EAAC;CACrB,CAAC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE;CAC/B,EAAE,OAAO,sBAAsB,CAAC,OAAO,EAAE,UAAU,CAAC;CACpD,EAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;AACD;CACA,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK;CACnD,CAAC,OAAO,WAAW;CACnB,EAAE,IAAI,QAAQ,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAChC,EAAE,IAAI,QAAQ,KAAK,CAAC;CACpB,GAAG,OAAO,KAAK,EAAE;CACjB,EAAE,IAAI,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,QAAQ,IAAI,CAAC,EAAC;CAClF,EAAE,IAAI,SAAS,GAAG,iBAAiB,CAAC,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAC;CAC/D,EAAE,IAAI,CAAC,SAAS,EAAE;CAClB,GAAG,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,EAAE,CAAC;CACxD,GAAG;CACH,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;CACrB,GAAG,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAC;CAC7D,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE;CACzB,EAAE;CACF,EAAC;AACD;CACO,SAAS,cAAc,GAAG;CACjC,CAAC,IAAI,gBAAgB,GAAGI,WAAS,CAAC,MAAM;CACxC;CACA,EAAE,GAAG,GAAG,KAAI;CACZ,EAAE,OAAO,cAAc,CAAC,aAAa,EAAE;CACvC,EAAE,EAAC;CACH,CAAC,OAAO,iBAAiB,GAAG,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;CAChG,CAAC;AACD;CACA,IAAI,eAAe,GAAG,aAAY;CAClC,IAAI,WAAW,GAAG,aAAY;CAC9B,IAAI,YAAY,GAAG,aAAY;CAC/B,IAAI,YAAY,GAAG,aAAY;AAE/B;CACO,SAAS,YAAY,CAAC,cAAc,EAAE;CAE7C,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC,EAAC;CAChC,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,EAAC;CAC5B,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,EAAC;CAC7B,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,EAAC;CAC7B,CAAC,SAAS,UAAU,CAAC,YAAY,EAAE;CACnC,EAAE,OAAO,SAAS,UAAU,CAAC,MAAM,EAAE;CACrC,GAAG,IAAI,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE,EAAC;CACzC,GAAG,IAAI,MAAM,IAAI,IAAI,EAAE;CACvB,IAAI,IAAIH,gBAAc;CACtB,KAAK,OAAO,YAAY,CAAC,MAAM,CAAC;CAChC,IAAI,IAAI,UAAU,GAAG,GAAG,CAAC,WAAU;CACnC,IAAI,IAAI,UAAU,GAAG,cAAc,CAACD,UAAQ,GAAG,YAAY,GAAG,UAAU,EAAE,MAAM,GAAG,UAAU,EAAE,GAAG,CAAC,MAAM,EAAC;CAC1G,IAAI,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;CACvC,KAAK,MAAM,GAAG,WAAU;CACxB,KAAK,OAAO,GAAG,YAAW;CAC1B,KAAK,MAAM;CACX,KAAK,OAAO,GAAG,WAAU;CACzB,KAAK,cAAc,GAAG,EAAC;CACvB,KAAK,YAAY,GAAG,EAAC;CACrB,KAAK,MAAM,GAAG,OAAO,CAAC,CAAC,EAAC;CACxB,KAAK,IAAI,MAAM,KAAK,SAAS;CAC7B,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;CACjD,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,eAAe,GAAG,MAAM,CAAC,OAAM;CACtC,GAAG,IAAI,eAAe,IAAI,MAAM,EAAE;CAClC,IAAIA,UAAQ,IAAI,OAAM;CACtB,IAAI,OAAO,MAAM;CACjB,IAAI;CACJ,GAAG,SAAS,GAAG,OAAM;CACrB,GAAG,cAAc,GAAGA,WAAQ;CAC5B,GAAG,YAAY,GAAGA,UAAQ,GAAG,gBAAe;CAC5C,GAAGA,UAAQ,IAAI,OAAM;CACrB,GAAG,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC;CACjC,GAAG;CACH,EAAE;CACF,CAAC;CACD,SAAS,YAAY,CAAC,MAAM,EAAE;CAC9B,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,MAAM,GAAG,EAAE,EAAE;CAClB,EAAE,IAAI,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;CACtC,GAAG,OAAO,MAAM;CAChB,EAAE;CACF,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,OAAO;CAC3B,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC,CAAC;CACnE,CAAC,MAAM,GAAG,GAAGA,UAAQ,GAAG,OAAM;CAC9B,CAAC,MAAM,KAAK,GAAG,GAAE;CACjB,CAAC,MAAM,GAAG,GAAE;CACZ,CAAC,OAAOA,UAAQ,GAAG,GAAG,EAAE;CACxB,EAAE,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC/B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;CAC5B;CACA,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpB,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,EAAC;CAC5C,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,EAAC;CAC5D,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAK;CAClF,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;CACtB,IAAI,IAAI,IAAI,QAAO;CACnB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,MAAM,EAAC;CAChD,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,EAAC;CAClC,IAAI;CACJ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnB,GAAG,MAAM;CACT,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpB,GAAG;AACH;CACA,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,EAAE;CAC9B,GAAG,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;CAC9C,GAAG,KAAK,CAAC,MAAM,GAAG,EAAC;CACnB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CACvB,EAAE,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;CAC7C,EAAE;AACF;CACA,CAAC,OAAO,MAAM;CACd,CAAC;CACM,SAAS,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;CAClD,CAAC,IAAI,WAAW,GAAG,GAAG,CAAC;CACvB,CAAC,GAAG,GAAG,MAAM,CAAC;CACd,CAACA,UAAQ,GAAG,KAAK,CAAC;CAClB,CAAC,IAAI;CACL,EAAE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;CAC9B,EAAE,SAAS;CACX,EAAE,GAAG,GAAG,WAAW,CAAC;CACpB,EAAE;CACF,CAAC;AACD;CACA,SAAS,SAAS,CAAC,MAAM,EAAE;CAC3B,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;CAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;CACnB,EAAE;CACF,CAAC,IAAI,cAAc,CAAC,UAAU;CAC9B,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;CAC7B,CAAC,OAAO,KAAK;CACb,CAAC;AACD;CACA,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,IAAI,cAAc,CAAC,aAAa,EAAE;CACnC,EAAE,IAAI,MAAM,GAAG,GAAE;CACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACnC,GAAG,IAAI,GAAG,GAAG,OAAO,GAAE;CACtB,GAAG,IAAI,GAAG,KAAK,WAAW;CAC1B,IAAI,GAAG,GAAG,UAAU,CAAC;CACrB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACvB,GAAG;CACH,EAAE,OAAO,MAAM;CACf,EAAE,MAAM;CACR,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACrB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACnC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;CAC1B,GAAG;CACH,EAAE,OAAO,GAAG;CACZ,EAAE;CACF,CAAC;AACD;CACA,IAAI,YAAY,GAAG,MAAM,CAAC,aAAY;CACtC,SAAS,cAAc,CAAC,MAAM,EAAE;CAChC,CAAC,IAAI,KAAK,GAAGA,WAAQ;CACrB,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;CAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,MAAM,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CAC/B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,IAAIA,UAAQ,GAAG,MAAK;CACpB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAI;CAClB,GAAG;CACH,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;CAC1C,CAAC;CACD,SAAS,eAAe,CAAC,MAAM,EAAE;CACjC,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;CACjB,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,IAAI,MAAM,KAAK,CAAC;CACnB,IAAI,OAAO,EAAE;CACb,QAAQ;CACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC;CAC1B,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,CAAC;CACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;CAC7B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACvB,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC/B,GAAG;CACH,EAAE,MAAM;CACR,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC5E,GAAGA,UAAQ,IAAI,EAAC;CAChB,GAAG,MAAM;CACT,GAAG;CACH,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,IAAI,MAAM,KAAK,CAAC;CACnB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnC,QAAQ;CACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACtC,IAAI;CACJ,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;CACzB,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,CAAC;CACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACzC,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACvB,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3C,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC7E,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,EAAE,EAAE;CACpB,IAAI,IAAI,MAAM,KAAK,CAAC;CACpB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAChD,SAAS;CACT,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,MAAMA,UAAQ,IAAI,EAAC;CACnB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnD,KAAK;CACL,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC1C,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,MAAM,GAAG,EAAE;CACnB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACtD,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACxD,IAAI,MAAM;CACV,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC9E,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,MAAM,GAAG,EAAE,EAAE;CACrB,KAAK,IAAI,MAAM,KAAK,EAAE;CACtB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC7D,UAAU;CACV,MAAM,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC7B,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC1B,OAAOA,UAAQ,IAAI,GAAE;CACrB,OAAO,MAAM;CACb,OAAO;CACP,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAChE,MAAM;CACN,KAAK,MAAM;CACX,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC3C,MAAMA,UAAQ,IAAI,GAAE;CACpB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,IAAI,MAAM,GAAG,EAAE;CACpB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnE,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,MAAMA,UAAQ,IAAI,GAAE;CACpB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACrE,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC;AACD;CACA,SAAS,gBAAgB,GAAG;CAC5B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;CACnB;CACA,EAAE,MAAM,GAAG,KAAK,GAAG,KAAI;CACvB,EAAE,MAAM;CACR,EAAE,OAAO,KAAK;CACd,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,IAAI,KAAK;CACT,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,KAAK;CACT,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,KAAK;CACT,GAAG;CACH,IAAI,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;CACtC,GAAG;CACH,EAAE;CACF,CAAC,OAAO,YAAY,CAAC,MAAM,CAAC;CAC5B,CAAC;AACD;AACA;CACA,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,OAAO,cAAc,CAAC,WAAW;CAClC;CACA,EAAE,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAEA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;CACpE,EAAE,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;CAC5C,CAAC;CACD,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,IAAI,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;CAC9B,EAAE,IAAI,IAAG;CACT,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,GAAG,IAAIA,UAAQ,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK;CACvG,GAAGA,UAAQ,GAAG,YAAY,CAAC;CAC3B,GAAG,IAAI;CACP,IAAI,OAAO,IAAI,EAAE,CAAC;CAClB,IAAI,SAAS;CACb,IAAIA,UAAQ,GAAG,GAAG,CAAC;CACnB,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;CACF;CACA,EAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;CACnD,CAAC;AACD;CACA,IAAI,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,EAAC;CAC9B,SAAS,OAAO,GAAG;CACnB,CAAC,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC7B,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;CACtC;CACA,EAAE,MAAM,GAAG,MAAM,GAAG,KAAI;CACxB,EAAE,IAAI,YAAY,IAAIA,UAAQ;CAC9B,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;CAC3F,OAAO,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;CAC/C,GAAG,OAAO,eAAe,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM;CACR,EAAEA,UAAQ,GAAE;CACZ,EAAE,OAAO,YAAY,CAAC,IAAI,EAAE,CAAC;CAC7B,EAAE;CACF,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,CAACA,UAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;CACjH,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAC;CAC1B,CAAC,IAAI,aAAa,GAAGA,WAAQ;CAC7B,CAAC,IAAI,GAAG,GAAGA,UAAQ,GAAG,MAAM,GAAG,EAAC;CAChC,CAAC,IAAI,MAAK;CACV,CAAC,IAAI,CAAC,GAAG,EAAC;CACV,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE;CACrC,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;CAC9B,GAAG,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;CAC5C,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;CAC5B,IAAI,aAAa,GAAG,WAAU;CAC9B,IAAI,KAAK;CACT,IAAI;CACJ,GAAG,aAAa,IAAI,EAAC;CACrB,GAAG;CACH,EAAE,GAAG,IAAI,EAAC;CACV,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;CAC9B,GAAG,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;CAC/B,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;CAC5B,IAAI,aAAa,GAAG,WAAU;CAC9B,IAAI,KAAK;CACT,IAAI;CACJ,GAAG;CACH,EAAE,IAAI,aAAa,KAAK,GAAG,EAAE;CAC7B,GAAGA,UAAQ,GAAG,cAAa;CAC3B,GAAG,OAAO,KAAK,CAAC,MAAM;CACtB,GAAG;CACH,EAAE,GAAG,IAAI,EAAC;CACV,EAAE,aAAa,GAAGA,WAAQ;CAC1B,EAAE;CACF,CAAC,KAAK,GAAG,GAAE;CACX,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAK;CACtB,CAAC,KAAK,CAAC,KAAK,GAAG,OAAM;CACrB,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;CAC7B,EAAE,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;CAC3C,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACnB,EAAE,aAAa,IAAI,EAAC;CACpB,EAAE;CACF,CAAC,GAAG,IAAI,EAAC;CACT,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;CAC7B,EAAE,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;CAC9B,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACnB,EAAE;CACF;CACA,CAAC,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;CAC5E,CAAC,IAAI,MAAM,IAAI,IAAI;CACnB,EAAE,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM;CAC9B,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;CAC9C,CAAC;AACD;CACA,SAAS,YAAY,CAAC,QAAQ,EAAE;CAChC;CACA,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC;CACnD,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC/H,CAAC,IAAI,QAAQ,IAAI,IAAI,EAAE,OAAO,QAAQ,GAAG,EAAE,CAAC;CAC5C,CAAC,IAAI,cAAc,CAAC,oBAAoB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE;CACvK,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;CACpC,EAAE;CACF,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzE,CAAC;CACD;CACA,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,QAAQ,KAAK;CAC3C,CAAC,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,YAAY,EAAC;CACzC;CACA,CAAC,IAAI,SAAS,GAAG,GAAE;CACnB,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;CAC7B,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,EAAC;CACjE,EAAE,SAAS,CAAC,QAAQ,GAAG,SAAQ;CAC/B,EAAE;CACF,CAAC,IAAI,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,EAAC;CAC9C;CACA;CACA;CACA,CAAC,IAAI,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE;CAC1E,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,kBAAiB;CAC7G,EAAE;CACF,CAAC,iBAAiB,CAAC,EAAE,CAAC,GAAG,UAAS;CAClC,CAAC,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAC;CAC7D,CAAC,OAAO,SAAS,CAAC,IAAI,EAAE;CACxB,EAAC;CACD,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,GAAE;CAC/B,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAI;AACpC;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC1B,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAChE,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3B,EAAE;CACF,CAAC,OAAO,KAAK,CAAC;CACd,EAAC;AACD;CACA,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;CAClD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;CAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;CAClB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;CAC/D,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;CACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC1C,CAAC,IAAI,CAAC,YAAY;CAClB,EAAE,YAAY,GAAG,IAAI,GAAG,GAAE;CAC1B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;CAC1B,CAAC,IAAI,OAAM;CACX;CACA,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;CACpE,EAAE,MAAM,GAAG,GAAE;CACb,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;CACzE,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;CACpB,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,KAAK,IAAI;CAC1G,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;CACpB;CACA,EAAE,MAAM,GAAG,GAAE;AACb;CACA,CAAC,IAAI,QAAQ,GAAG,EAAE,MAAM,GAAE;CAC1B,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAC;CAC/B,CAAC,IAAI,gBAAgB,GAAG,IAAI,GAAE;CAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;CACrB;CACA,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,gBAAgB;CAC3C,EAAE,MAAM;CACR;CACA,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAC;CACzC,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,YAAY,GAAG;CAC1B,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAC;CACjE,CAAC,IAAI,MAAM,YAAY,GAAG;CAC1B,EAAE,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAC;CAC3D,CAAC,OAAO,MAAM;CACd,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;CACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC1C,CAAC,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,EAAE,EAAC;CACpC,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAI;CACrB,CAAC,OAAO,QAAQ,CAAC,MAAM;CACvB,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,EAAC;AAC/C;CACO,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,OAAO,EAAC;AACnK;CACA,IAAI,IAAI,GAAG,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,MAAM,CAAC;CAChE,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAC;CACvB;CACA,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAM;AAC7D;CACA,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,QAAQ,EAAC;CAC3C,CAAC,IAAI,CAAC,cAAc,EAAE;CACtB,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,MAAM;CACpC,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC;CAClD,EAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,QAAQ,CAAC;CACpE,EAAE;CACF,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;CACxC,EAAC;CACD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;CAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;CAClB,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;CACpC,EAAC;CACD,MAAM,WAAW,GAAG,GAAE;CACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAC;CAC5E,CAAC,IAAI,YAAY,GAAGA,WAAQ;CAC5B,CAACA,UAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAM;CACnC,CAACC,gBAAc,GAAG,YAAW;CAC7B,CAACA,gBAAc,GAAG,CAAC,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,EAAC;CAC1D,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;CAC7B,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;CAC7B,CAACA,gBAAc,CAAC,kBAAkB,GAAGD,WAAQ;CAC7C,CAACA,UAAQ,GAAG,aAAY;CACxB,CAAC,OAAO,IAAI,EAAE;CACd,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;CACrB,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;CAC5F,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;CAC1B,EAAE,OAAO,IAAI,IAAI;CACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO;CAClF,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;CAC7G,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE;CAC3B,EAAE,OAAO,IAAI,IAAI;CACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO;CAC3E,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;CAC7K;CACA,EAAE,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;CAC5B,EAAC;CACD;CACA;AACA;CACA,SAASI,WAAS,CAAC,QAAQ,EAAE;CAC7B,CAAC,IAAI,WAAW;CAChB,EAAE,WAAW,EAAE,CAAC;CAChB,CAAC,IAAI,WAAW,GAAG,OAAM;CACzB,CAAC,IAAI,aAAa,GAAGJ,WAAQ;CAC7B,CAAC,IAAI,mBAAmB,GAAG,eAAc;CACzC,CAAC,IAAI,mBAAmB,GAAG,eAAc;CACzC,CAAC,IAAI,iBAAiB,GAAG,aAAY;CACrC,CAAC,IAAI,cAAc,GAAG,UAAS;CAC/B,CAAC,IAAI,YAAY,GAAG,QAAO;CAC3B,CAAC,IAAI,iBAAiB,GAAG,aAAY;CACrC,CAAC,IAAI,mBAAmB,GAAGC,iBAAc;AACzC;CACA;CACA,CAAC,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,EAAC;CACpD,CAAC,IAAI,eAAe,GAAG,kBAAiB;CACxC,CAAC,IAAI,uBAAuB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAC;CACnF,CAAC,IAAI,UAAU,GAAG,eAAc;CAChC,CAAC,IAAI,mBAAmB,GAAG,eAAc;CACzC,CAAC,IAAI,KAAK,GAAG,QAAQ,GAAE;CACvB,CAAC,MAAM,GAAG,YAAW;CACrB,CAACD,UAAQ,GAAG,cAAa;CACzB,CAAC,cAAc,GAAG,oBAAmB;CACrC,CAAC,cAAc,GAAG,oBAAmB;CACrC,CAAC,YAAY,GAAG,kBAAiB;CACjC,CAAC,SAAS,GAAG,eAAc;CAC3B,CAAC,OAAO,GAAG,aAAY;CACvB,CAAC,YAAY,GAAG,kBAAiB;CACjC,CAACC,gBAAc,GAAG,oBAAmB;CACrC,CAAC,GAAG,GAAG,SAAQ;CACf,CAAC,cAAc,GAAG,oBAAmB;CACrC,CAAC,iBAAiB,GAAG,gBAAe;CACpC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAE,GAAG,uBAAuB,EAAC;CAClF,CAAC,cAAc,GAAG,WAAU;CAC5B,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAC;CACpE,CAAC,OAAO,KAAK;CACb,CAAC;CACM,SAAS,WAAW,GAAG;CAC9B,CAAC,GAAG,GAAG,KAAI;CACX,CAAC,YAAY,GAAG,KAAI;CACpB,CAAC,iBAAiB,GAAG,KAAI;CACzB,CAAC;AACD;CACO,SAASK,cAAY,CAAC,SAAS,EAAE;CACxC,CAAC,IAAI,SAAS,CAAC,MAAM;CACrB,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAM;CACtD;CACA,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAS;CAC/C,CAAC;AACD;CACO,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,EAAC;CACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAC9B,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,EAAC;CACtD,CAAC;CAED,IAAI,cAAc,GAAG,IAAID,SAAO,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;CAChD,MAAME,QAAM,GAAG,cAAc,CAAC,OAAM;CACpC,MAAMC,gBAAc,GAAG,cAAc,CAAC,eAAc;CACrC,cAAc,CAAC,OAAM;CACpC,MAAM,eAAe,GAAG;CAC/B,CAAC,KAAK,EAAE,CAAC;CACT,CAAC,MAAM,EAAE,CAAC;CACV,CAAC,aAAa,EAAE,CAAC;CACjB,CAAC,WAAW,EAAE,CAAC;CACf,EAAC;CACD,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,EAAC;CAClC,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAC;CAC5C,SAASC,cAAY,CAAC,aAAa,EAAE;CAC5C,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAa;CAC5B,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CACxE,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,aAAa,IAAI,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;CAC3F,CAAC;CACM,SAAS,aAAa,CAAC,iBAAiB,EAAE,aAAa,EAAE,SAAS,EAAE;CAC3E,CAACP,YAAU,GAAG,iBAAiB,CAAC;CAChC,CAACC,oBAAkB,GAAG,aAAa,CAAC;CACpC,CAAC,WAAW,GAAG,SAAS,CAAC;CACzB;;CCzqCA,IAAIO,cAAW;CACf,IAAI;CACJ,CAACA,aAAW,GAAG,IAAI,WAAW,GAAE;CAChC,CAAC,CAAC,OAAO,KAAK,EAAE,EAAE;CAClB,IAAI,UAAU,EAAE,iBAAgB;CAChC,MAAMC,eAAa,GAAG,OAAO,MAAM,KAAK,YAAW;CACnD,MAAM,iBAAiB,GAAGA,eAAa;CACvC,CAAC,SAAS,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,GAAG,WAAU;CACxE,MAAM,SAAS,GAAGA,eAAa,GAAG,MAAM,GAAG,WAAU;CACrD,MAAM,eAAe,GAAGA,eAAa,GAAG,WAAW,GAAG,WAAU;CAChE,IAAI,MAAM,EAAE,WAAU;CACtB,IAAI,WAAU;CACd,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI,QAAO;CACX,IAAI,cAAc,GAAG,KAAI;CACzB,IAAI,iBAAgB;CACpB,MAAM,eAAe,GAAG,OAAM;CAC9B,MAAM,WAAW,GAAG,kBAAiB;CAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,EAAC;eACzC,MAAM,KAAK,SAASN,SAAO,CAAC;CACnC,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,KAAK,CAAC,OAAO,EAAC;CAChB,EAAE,IAAI,CAAC,MAAM,GAAG,EAAC;CAEjB,EAAE,IAAI,MAAK;CACX,EAAE,IAAI,gBAAe;CACrB,EAAE,IAAI,WAAU;CAChB,EAAE,IAAI,aAAY;CAClB,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;CAC9E,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;CAC1E,GAAG,GAAG,CAACK,aAAW,IAAIA,aAAW,CAAC,UAAU;CAC5C,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;CAC9B,IAAI,OAAOA,aAAW,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;CAC5E,IAAI,GAAG,MAAK;AACZ;CACA,EAAE,IAAI,KAAK,GAAG,KAAI;CAClB,EAAE,IAAI,CAAC,OAAO;CACd,GAAG,OAAO,GAAG,GAAE;CACf,EAAE,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,WAAU;CAClD,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAc;CACxE,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,oBAAmB;CACvD,EAAE,IAAI,mBAAmB,IAAI,IAAI;CACjC,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,EAAE,GAAG,EAAC;CACrD,EAAE,IAAI,mBAAmB,GAAG,IAAI;CAChC,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;CACxD,EAAE,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,IAAI,SAAS,EAAE;CACjE,GAAG,IAAI,CAAC,SAAS,GAAG,KAAI;CACxB,GAAG;CACH,EAAE,IAAI,gBAAgB,GAAG,OAAO,CAAC,iBAAgB;CACjD,EAAE,IAAI,gBAAgB,IAAI,IAAI;CAC9B,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,GAAG,GAAE;CACnD,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK;CACrD,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;CACvB;CACA,EAAE,IAAI,iBAAiB,GAAG,mBAAmB,GAAG,EAAE,KAAK,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAC;CACnG,EAAE,IAAI,aAAa,GAAG,mBAAmB,GAAG,KAAI;CAChD,EAAE,IAAI,cAAc,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,KAAI;CACpE,EAAE,IAAI,cAAc,GAAG,IAAI,EAAE;CAC7B,GAAG,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;CAC1E,GAAG;CACH,EAAE,IAAI,iBAAiB,GAAG,GAAE;CAC5B,EAAE,IAAI,gBAAgB,GAAG,EAAC;CAC1B,EAAE,IAAI,oCAAoC,GAAG,EAAC;AAC9C;CACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE,aAAa,EAAE;CAC3D,GAAG,IAAI,CAAC,MAAM,EAAE;CAChB,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;CACxC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAC;CAC5F,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI;CACJ,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAC/B,GAAG,IAAI,OAAO,GAAG,QAAQ,GAAG,KAAK,EAAE;CACnC;CACA,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAC;CACjD,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAC;CACrG,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAChC,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI;CACJ,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,WAAU;CAC1C,GAAG,KAAK,GAAG,SAAQ;CACnB,GAAG,IAAI,aAAa,GAAG,mBAAmB,EAAE,QAAQ,KAAK,aAAa,GAAG,IAAI,EAAC;CAC9E,GAAG,YAAY,GAAG,KAAK,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,GAAG,KAAI;CAC1D,GAAG,IAAI,KAAK,CAAC,aAAa,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACzD,IAAI,cAAc,GAAG,GAAE;CACvB,IAAI,cAAc,CAAC,IAAI,GAAG,SAAQ;CAClC,IAAI;CACJ,IAAI,cAAc,GAAG,KAAI;CACzB,GAAG,UAAU,GAAG,KAAK,CAAC,WAAU;CAChC,GAAG,IAAI,UAAU,EAAE;CACnB,IAAI,IAAI,UAAU,CAAC,aAAa;CAChC,KAAK,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,EAAE,EAAC;CAC/D,IAAI,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CACnD,IAAI,IAAI,YAAY,GAAG,mBAAmB,EAAE;CAC5C;CACA,KAAK,MAAM,IAAI,KAAK,CAAC,oGAAoG,GAAG,UAAU,CAAC,YAAY,CAAC;CACpJ,KAAK;CACL,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;CACjC;CACA,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CACjD,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;CAC5C,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,EAAC;CAC9B,MAAM,IAAI,CAAC,IAAI;CACf,OAAO,QAAQ;CACf,MAAM,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,YAAW;CAC7D,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACvC,OAAO,IAAI,CAAC,cAAc,EAAE;CAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC9D,QAAQ;CACR,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO;CACP,MAAM,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAI;CAC1C,MAAM;CACN,KAAK,IAAI,CAAC,yBAAyB,GAAG,aAAY;CAClD,KAAK;CACL,IAAI,IAAI,CAAC,YAAY,EAAE;CACvB,KAAK,UAAU,CAAC,MAAM,GAAG,YAAY,GAAG,KAAI;CAC5C,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,eAAe;CACtB,IAAI,eAAe,GAAG,MAAK;CAC3B,GAAG,IAAI,aAAa,CAAC;CACrB,GAAG,IAAI;CACP,IAAI,IAAI,KAAK,CAAC,qBAAqB,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM;CACjG,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC;CACxB;CACA,KAAK,IAAI,CAAC,KAAK,EAAC;CAChB,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC;CACpC,IAAI,IAAI,cAAc;CACtB,KAAK,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;CACjC,IAAI,IAAI,YAAY,IAAI,YAAY,CAAC,WAAW,EAAE;CAClD,KAAK,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC7F,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAChC,KAAK,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;CAChC,KAAK,OAAO,UAAU,IAAI,CAAC,GAAG,CAAC,EAAE;CACjC,MAAM,IAAI,cAAc,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;CAC3D,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,iBAAiB,KAAK,CAAC,CAAC;CAC3F,OAAO,iBAAiB,GAAG,CAAC,CAAC;CAC7B,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;CAC1D,OAAO,IAAI,iBAAiB,IAAI,CAAC;CACjC,QAAQ,iBAAiB,IAAI,CAAC,CAAC;CAC/B,OAAO,MAAM;CACb,OAAO,IAAI,iBAAiB,IAAI,CAAC,EAAE;CACnC;CACA,QAAQ,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;CACxD,SAAS,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;CAC/E,QAAQ,iBAAiB,GAAG,CAAC,CAAC,CAAC;CAC/B,QAAQ;CACR,OAAO,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;CACxC,OAAO,CAAC,EAAE,CAAC;CACX,OAAO;CACP,MAAM;CACN,KAAK,IAAI,iBAAiB,IAAI,CAAC,IAAI,UAAU,EAAE;CAC/C;CACA,MAAM,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;CACtD,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;CAC7E,MAAM;CACN,KAAK,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;CACxC,KAAK,IAAI,QAAQ,GAAG,OAAO;CAC3B,MAAM,QAAQ,CAAC,QAAQ,EAAC;CACxB,KAAK,KAAK,CAAC,MAAM,GAAG,SAAQ;CAC5B,KAAK,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAC;CAC9E,KAAK,YAAY,GAAG,KAAI;CACxB,KAAK,OAAO,UAAU;CACtB,KAAK;CACL,IAAI,KAAK,CAAC,MAAM,GAAG,SAAQ;CAC3B,IAAI,IAAI,aAAa,GAAG,iBAAiB,EAAE;CAC3C,KAAK,MAAM,CAAC,KAAK,GAAG,MAAK;CACzB,KAAK,MAAM,CAAC,GAAG,GAAG,SAAQ;CAC1B,KAAK,OAAO,MAAM;CAClB,KAAK;CACL,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;CAC3C,IAAI,CAAC,MAAM,KAAK,EAAE;CAClB,IAAI,aAAa,GAAG,KAAK,CAAC;CAC1B,IAAI,MAAM,KAAK,CAAC;CAChB,IAAI,SAAS;CACb,IAAI,IAAI,UAAU,EAAE;CACpB,KAAK,eAAe,EAAE,CAAC;CACvB,KAAK,IAAI,eAAe,IAAI,KAAK,CAAC,cAAc,EAAE;CAClD,MAAM,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CACrD;CACA,MAAM,IAAI,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAC;CACzD,MAAM,IAAI,aAAa,GAAGE,mBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;CAC/D,MAAM,IAAI,CAAC,aAAa,EAAE;CAC1B,OAAO,IAAI,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,CAAC,YAAY,CAAC,KAAK,KAAK,EAAE;CACtF;CACA,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;CAC/C,QAAQ;CACR,OAAO,KAAK,CAAC,yBAAyB,GAAG,aAAY;CACrD;CACA,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;CACpD,OAAO,OAAO,YAAY;CAC1B,OAAO;CACP,MAAM;CACN,KAAK;CACL;CACA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;CACjD,IAAI,IAAI,aAAa,GAAG,iBAAiB;CACzC,KAAK,QAAQ,GAAG,MAAK;CACrB,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,eAAe,GAAG,MAAM;CAChC,GAAG,IAAI,oCAAoC,GAAG,EAAE;CAChD,IAAI,oCAAoC,GAAE;CAC1C,GAAG,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CAClD,GAAG,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,IAAI,CAAC,YAAY;CACxD,IAAI,UAAU,CAAC,MAAM,GAAG,aAAY;CACpC,GAAG,IAAI,gBAAgB,GAAG,KAAK,EAAE;CACjC;CACA,IAAI,UAAU,CAAC,WAAW,GAAG,KAAI;CACjC,IAAI,oCAAoC,GAAG,EAAC;CAC5C,IAAI,gBAAgB,GAAG,EAAC;CACxB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;CACpC,KAAK,iBAAiB,GAAG,GAAE;CAC3B,IAAI,MAAM,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE;CAC7D,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9D,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,EAAC;CAC5C,KAAK;CACL,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;CAC/B,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC,OAAM;CAC5B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;CACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC1C,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI;CACJ,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;CAClB,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;CAC1B,GAAG,IAAI,QAAQ,GAAG,OAAO;CACzB,IAAI,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAC/B;CACA,GAAG,IAAI,IAAI,GAAG,OAAO,MAAK;CAC1B,GAAG,IAAI,OAAM;CACb,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE;CAC1B,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,OAAM;CAChC,IAAI,IAAI,cAAc,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,MAAM,EAAE;CAChE,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,SAAS,IAAI,eAAe,EAAE;CAC/D,MAAM,IAAI,SAAQ;CAClB,MAAM,IAAI,QAAQ,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,GAAE;CAC3G,MAAM,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;CACvC,OAAO,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;CAC7C,MAAM,IAAI,WAAU;CACpB,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE;CACnC,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;CAC9B,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;CAClC,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;CACnC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,GAAG,QAAQ,EAAC;CAC9E,OAAO,MAAM;CACb,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;CAClC,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO;CACP,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,EAAE,EAAC;CAC/B,MAAM,cAAc,CAAC,QAAQ,GAAG,UAAU,CAAC;CAC3C,MAAM,cAAc,CAAC,IAAI,GAAG,EAAC;CAC7B,MAAM,cAAc,CAAC,QAAQ,GAAG,SAAQ;CACxC,MAAM;CACN,KAAK,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,EAAC;CAC1C,KAAK,cAAc,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;CAC7C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;CAC5C,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,WAAU;CAClB;CACA,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;CAC1B,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,EAAE;CAClC,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM,IAAI,SAAS,GAAG,OAAO,EAAE;CACpC,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM;CACX,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK;CACL,IAAI,IAAI,QAAQ,GAAG,SAAS,GAAG,EAAC;CAChC,IAAI,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;CACrC,KAAK,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;AAC3C;CACA,IAAI,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACzC,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,GAAG,QAAQ,GAAG,WAAU;CACvD,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;CACrC,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;CAC9B,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE;CACrB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAE;CACjC,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;CAC7B,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,KAAI;CAC7C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,MAAM;CAC/B,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,MAAM,MAAM;CAC3D,QAAQ;CACR,OAAO,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC;CAC3D,OAAO,CAAC,GAAE;CACV,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAI;CACrD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO;CACP,MAAM;CACN,KAAK,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAU;CACjD,KAAK,MAAM;CACX,KAAK,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,QAAQ,GAAG,UAAU,EAAC;CACtD,KAAK;AACL;CACA,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACvC,KAAK,MAAM,IAAI,MAAM,GAAG,KAAK,EAAE;CAC/B,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAChC,KAAK,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CACjC,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACrC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACvC,KAAK,MAAM;CACX,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC3C,KAAK,QAAQ,IAAI,EAAC;CAClB,KAAK;CACL,IAAI,QAAQ,IAAI,OAAM;CACtB,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;CACjC,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,EAAE;CAC/B;CACA,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;CACvH,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;CAChC,MAAM,MAAM,IAAI,KAAK,GAAG,KAAK,EAAE;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;CAChC,MAAM,MAAM,IAAI,KAAK,GAAG,OAAO,EAAE;CACjC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,IAAI,EAAC;CACrC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,KAAI;CACvC,MAAM,MAAM;CACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC3C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM;CACN,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;CACrC,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;CACzB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;CACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;CAChC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;CACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;CAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC1C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM,MAAM;CACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC1C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM;CACN,KAAK,MAAM;CACX,KAAK,IAAI,WAAU;CACnB,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE;CAC5F,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC5C,MAAM,IAAI,SAAQ;CAClB,MAAM,IAAI,UAAU,GAAG,CAAC;CACxB;CACA,QAAQ,CAAC,CAAC,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE;CACzH,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM;CACb,OAAO;CACP,OAAO,QAAQ,GAAE;CACjB,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC3C,KAAK,QAAQ,IAAI,EAAC;CAClB,KAAK;CACL,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;CACxD,IAAI,IAAI,CAAC,KAAK;CACd,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,SAAS;CACT,KAAK,IAAI,YAAY,EAAE;CACvB,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAC;CAC3C,MAAM,IAAI,OAAO,EAAE;CACnB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;CACxB,QAAQ,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,KAAK,YAAY,CAAC,WAAW,GAAG,EAAE,EAAC;CACrF,QAAQ,OAAO,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC;CAC9C,QAAQ;CACR,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,EAAC;CACjD,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM;CACb,OAAO;CACP,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAC;CAC5D,MAAM;CACN,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC,YAAW;CACxC,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE;CACjC,MAAM,WAAW,CAAC,KAAK,EAAC;CACxB,MAAM,MAAM,IAAI,WAAW,KAAK,KAAK,EAAE;CACvC,MAAM,SAAS,CAAC,KAAK,EAAC;CACtB,MAAM,MAAM,IAAI,WAAW,KAAK,GAAG,EAAE;CACrC,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1D,WAAW;CACX,OAAO,MAAM,GAAG,KAAK,CAAC,KAAI;CAC1B,OAAO,IAAI,MAAM,GAAG,IAAI,EAAE;CAC1B,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CAC1C,QAAQ,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CACpC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACjC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACxC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CAC1C,QAAQ,MAAM;CACd,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACjC,QAAQ,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC9C,QAAQ,QAAQ,IAAI,EAAC;CACrB,QAAQ;CACR,OAAO,KAAK,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,KAAK,EAAE;CAC5C,QAAQ,IAAI,CAAC,GAAG,EAAC;CACjB,QAAQ,IAAI,CAAC,UAAU,EAAC;CACxB,QAAQ;CACR,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACzD,OAAO,IAAI,cAAc,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC/C,OAAO,IAAI,KAAK,YAAY,cAAc,EAAE;CAC5C,QAAQ,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,EAAC;CACrC,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;CAC7B,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE;CAC7B,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACnC,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,KAAI;CAC7C,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAChC,UAAU;CACV,SAAS,IAAI,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC;CAC5D,SAAS,IAAI,WAAW,KAAK,KAAK,EAAE;CACpC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CACpC,WAAW,SAAS,CAAC,KAAK,EAAC;CAC3B,WAAW,MAAM;CACjB,WAAW,WAAW,CAAC,KAAK,EAAC;CAC7B,WAAW;CACX,UAAU,MAAM;CAChB,UAAU,IAAI,CAAC,WAAW,EAAC;CAC3B,UAAU;CACV,SAAS,MAAM;CACf,SAAS;CACT,QAAQ,IAAI,aAAa,GAAG,OAAM;CAClC,QAAQ,IAAI,iBAAiB,GAAG,WAAU;CAC1C,QAAQ,IAAI,eAAe,GAAG,SAAQ;CACtC,QAAQ,MAAM,GAAG,KAAI;CACrB,QAAQ,IAAI,OAAM;CAClB,QAAQ,IAAI;CACZ,SAAS,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK;CAC7D;CACA,UAAU,MAAM,GAAG,cAAa;CAChC,UAAU,aAAa,GAAG,KAAI;CAC9B,UAAU,QAAQ,IAAI,KAAI;CAC1B,UAAU,IAAI,QAAQ,GAAG,OAAO;CAChC,WAAW,QAAQ,CAAC,QAAQ,EAAC;CAC7B,UAAU,OAAO;CACjB,WAAW,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;CACxD,WAAW;CACX,UAAU,EAAE,IAAI,EAAC;CACjB,SAAS,SAAS;CAClB;CACA,SAAS,IAAI,aAAa,EAAE;CAC5B,UAAU,MAAM,GAAG,cAAa;CAChC,UAAU,UAAU,GAAG,kBAAiB;CACxC,UAAU,QAAQ,GAAG,gBAAe;CACpC,UAAU,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CACtC,UAAU;CACV,SAAS;CACT,QAAQ,IAAI,MAAM,EAAE;CACpB,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;CAC/C,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,EAAC;CAC5C,SAAS,QAAQ,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAC;CAChF,SAAS;CACT,QAAQ,MAAM;CACd,QAAQ;CACR,OAAO;CACP;CACA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CAChC,OAAO,SAAS,CAAC,KAAK,EAAC;CACvB,OAAO,MAAM;CACb;CACA,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;CACzB,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAE;CACnC;CACA,QAAQ,IAAI,IAAI,KAAK,KAAK;CAC1B,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC;CAC1B,QAAQ;AACR;CACA;CACA,OAAO,IAAI,IAAI,KAAK,UAAU;CAC9B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE;CACA;CACA,OAAO,WAAW,CAAC,KAAK,EAAC;CACzB,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;CAClC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAI;CAC5C,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;CACjC,IAAI,IAAI,KAAK,GAAG,kBAAkB,IAAI,KAAK,IAAI,CAAC,kBAAkB,EAAE;CACpE;CACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC5C,KAAK,MAAM,IAAI,KAAK,GAAG,mBAAmB,IAAI,KAAK,GAAG,CAAC,EAAE;CACzD;CACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC7C,KAAK,MAAM;CACX;CACA,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE;CAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,EAAC;CACpD,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;CAC1C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;CACpC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;CACtI,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,QAAQ,EAAE,CAAC;CACjB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;CACrB,MAAM,IAAI,WAAW,CAAC;CACtB,MAAM,GAAG;CACT,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;CACvC,OAAO,WAAW,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9F,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACxB,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;CAC3B,OAAO,QAAQ,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;CAChF,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;CACxC,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG;CACzC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/C,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,MAAM;CACZ,MAAM,MAAM,IAAI,UAAU,CAAC,KAAK,GAAG,iEAAiE;CACpG,OAAO,+EAA+E;CACtF,OAAO,2CAA2C,CAAC;CACnD,MAAM;CACN,KAAK;CACL,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI,MAAM,IAAI,IAAI,KAAK,WAAW,EAAE;CACpC,IAAI,IAAI,IAAI,CAAC,oBAAoB;CACjC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,SAAS;CACT,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAC3B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAC3B,KAAK;CACL,IAAI,MAAM;CACV,IAAI,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC5C,IAAI;CACJ,IAAG;AACH;CACA,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,KAAK;CAChH;CACA,GAAG,IAAI,IAAI,CAAC;CACZ,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACxB,IAAI,IAAI,GAAG,EAAE,CAAC;CACd,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC5B,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC;CACnF,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;CAC5C,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrB,KAAK;CACL,IAAI,MAAM;CACV,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;CAC9B,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;CAC3B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;CACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC1C,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI;CACJ,GAAG,IAAI,IAAG;CACV,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE;CAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACrC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CAClB,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAAC;CAC1B,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC;CACjC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;AACL;CACA,IAAI,MAAM;CACV,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACrC,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAC;CACxB,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,IAAI,YAAY,GAAG,QAAQ,GAAG,MAAK;CACtC,GAAG,QAAQ,IAAI,EAAC;CAChB,GAAG,IAAI,IAAI,GAAG,EAAC;CACf,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,IAAI,CAAC,GAAG,EAAC;CACd,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK,IAAI,GAAE;CACX,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;CACtB,IAAI,MAAM,IAAI,KAAK,CAAC,6DAA6D;CACjF,IAAI,4DAA4D,CAAC,CAAC;CAClE,IAAI;CACJ,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,EAAC;CAC7C,GAAG,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,KAAI;CAC7C,IAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,KAAK,KAAK,GAAG,gBAAgB;CAClE,EAAE,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,iBAAiB;CACnD,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;CAC5G,GAAG,IAAI,YAAY,GAAG,QAAQ,EAAE,GAAG,MAAK;CACxC,GAAG,IAAI,UAAS;CAChB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACrC,KAAK,IAAI,cAAc;CACvB,MAAM,UAAU,GAAG,eAAc;CACjC,UAAU;CACV;CACA,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;CACpC,MAAM,IAAI,cAAc,GAAG,WAAU;CACrC,MAAM,UAAU,GAAG,UAAU,CAAC,YAAW;CACzC,MAAM,IAAI,cAAc,GAAG,EAAC;CAC5B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACvC,OAAO,IAAI,CAAC,cAAc,EAAE;CAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC9D,QAAQ,cAAc,GAAE;CACxB,QAAQ;CACR,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO;CACP,MAAM,IAAI,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,QAAQ,EAAE;CAChD;CACA,OAAO,QAAQ,GAAE;CACjB,OAAO,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;CAClD,OAAO;CACP,OAAO,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,EAAC;CACtE,MAAM,SAAS,GAAG,KAAI;CACtB,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,EAAC;CACtC,MAAM;CACN,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,CAAC,SAAS,EAAE;CACnB,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;CAC5C,IAAI,IAAI,QAAQ;CAChB,KAAK,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,SAAQ;CAC5C;CACA,KAAK,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC;CACtE,IAAI;CACJ,GAAG;CACH,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;CAC5G,GAAG,IAAI,cAAc,GAAG,EAAC;CACzB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CAC1G,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACpC,IAAI,IAAI,CAAC,cAAc,EAAE;CACzB,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC3D,KAAK,cAAc,GAAE;CACrB,KAAK;CACL,IAAI,UAAU,GAAG,eAAc;CAC/B,IAAI;CACJ,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;CAC3C,GAAG,IAAI,QAAQ,EAAE;CACjB,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,iBAAiB,EAAE;CAC/C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAI;CAC5D,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAC;CACvC,KAAK;CACL,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,IAAI,MAAM;CACV,IAAI,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,cAAc,EAAC;CACrF,IAAI;CACJ;CACA,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM;CACzB,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAG;AACH;CACA;CACA,EAAE,MAAM,eAAe,GAAG,OAAO,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AAClF;CACA,EAAE,MAAM,WAAW,GAAG,eAAe,GAAG,CAAC,MAAM,KAAK;CACpD,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAC;CAC3E,GAAG,GAAG,YAAW;AACjB;CACA,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;CAC5B,GAAG,IAAI,QAAO;CACd,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE;CACxB;CACA,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,eAAe;CACvC,KAAK,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC;CAC9E,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe;CACtC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAC;CACpG,IAAI;CACJ,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAE;CACjF,GAAG,IAAI,SAAS,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAC;CACjD,GAAG,UAAU,GAAG,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,EAAC;CACvG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAC;CACrC,GAAG,IAAI,MAAM,CAAC,IAAI;CAClB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC;CACzC;CACA,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAC;CAC3C,GAAG,QAAQ,IAAI,MAAK;CACpB,GAAG,KAAK,GAAG,EAAC;CACZ,GAAG,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,GAAE;CAClC,GAAG,OAAO,MAAM,GAAG,SAAS;CAC5B,IAAG;CACH,EAAE,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,KAAK;CAC1D,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAM;CACnC,GAAG,IAAI,CAAC,QAAQ;CAChB,IAAI,QAAQ,GAAG,KAAI;CACnB,GAAG,IAAI,QAAQ,GAAG,aAAa,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;CAClG,IAAI,QAAQ,GAAG,UAAU,CAAC,UAAS;CACnC,IAAI,IAAI,EAAE,QAAQ,GAAG,cAAc,CAAC;CACpC,KAAK,QAAQ,GAAG,cAAa;CAC7B,IAAI,UAAU,CAAC,SAAS,GAAG,QAAQ,GAAG,EAAC;CACvC,IAAI,MAAM;CACV,IAAI,IAAI,QAAQ,IAAI,cAAc;CAClC,KAAK,QAAQ,GAAG,cAAa;CAC7B,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAC;CACpC,IAAI;CACJ,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAC;CACrG,GAAG,UAAU,CAAC,aAAa,CAAC,GAAG,SAAQ;CACvC,GAAG,UAAU,CAAC,QAAQ,GAAG,KAAI;CAC7B,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAI;AACrC;CACA,GAAG,IAAI,QAAQ,GAAG,aAAa,EAAE;CACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAI;CACxB,IAAI,UAAU,CAAC,YAAY,GAAG,QAAQ,GAAG,KAAI;CAC7C,IAAI,eAAe,GAAG,KAAI;CAC1B,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;CAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK,MAAM;CACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK;CACL,IAAI,MAAM;CACV,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;CAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK,MAAM;CACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK;AACL;CACA,IAAI,IAAI,cAAc;CACtB,KAAK,gBAAgB,IAAI,oCAAoC,GAAG,eAAc;CAC9E;CACA,IAAI,IAAI,iBAAiB,CAAC,MAAM,IAAI,gBAAgB;CACpD,KAAK,iBAAiB,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,GAAG,EAAC;CACjD,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAC;CACtC,IAAI,IAAI,CAAC,IAAI,EAAC;CACd,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,cAAc,KAAK;CACjF,GAAG,IAAI,UAAU,GAAG,OAAM;CAC1B,GAAG,IAAI,YAAY,GAAG,SAAQ;CAC9B,GAAG,IAAI,WAAW,GAAG,QAAO;CAC5B,GAAG,IAAI,SAAS,GAAG,MAAK;CACxB,GAAG,MAAM,GAAG,WAAU;CACtB,GAAG,QAAQ,GAAG,EAAC;CACf,GAAG,KAAK,GAAG,EAAC;CACZ,GAAG,IAAI,CAAC,MAAM;CACd,IAAI,UAAU,GAAG,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;CACrD,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAC/B,GAAG,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;CAC9C,GAAG,UAAU,GAAG,OAAM;CACtB,GAAG,IAAI,YAAY,GAAG,SAAQ;CAC9B,GAAG,MAAM,GAAG,WAAU;CACtB,GAAG,QAAQ,GAAG,aAAY;CAC1B,GAAG,OAAO,GAAG,YAAW;CACxB,GAAG,KAAK,GAAG,UAAS;CACpB,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE;CACzB,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,EAAC;CAC5C,IAAI,IAAI,MAAM,GAAG,OAAO;CACxB,KAAK,QAAQ,CAAC,MAAM,EAAC;CACrB,IAAI,IAAI,iBAAiB,GAAG,eAAe,GAAG,MAAK;CACnD,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,GAAG,YAAY,EAAE,iBAAiB,GAAG,CAAC,EAAE,QAAQ,EAAC;CACxF,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,iBAAiB,EAAC;CACpE,IAAI,QAAQ,GAAG,OAAM;CACrB,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,EAAC;CACnD,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,KAAK;CAClC,GAAG,IAAI,WAAW,GAAG,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,kBAAkB,KAAK;CACzI,IAAI,IAAI,kBAAkB;CAC1B,KAAK,OAAO,eAAe,GAAG,IAAI,CAAC;CACnC,IAAI,QAAQ,GAAG,WAAW,CAAC;CAC3B,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC;CAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;CAChB,IAAI,eAAe,EAAE,CAAC;CACtB,IAAI,IAAI,WAAW,KAAK,MAAM,EAAE;CAChC,KAAK,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;CAC7C,KAAK;CACL,IAAI,OAAO,QAAQ,CAAC;CACpB,IAAI,EAAE,IAAI,CAAC,CAAC;CACZ,GAAG,IAAI,WAAW,KAAK,CAAC;CACxB,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;CAC/B,GAAG,QAAQ,GAAG,WAAW,CAAC;CAC1B,IAAG;CACH,EAAE;CACF,CAAC,SAAS,CAAC,MAAM,EAAE;CACnB;CACA,EAAE,MAAM,GAAG,OAAM;CACjB,EAAE,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;CAC1G,EAAE,QAAQ,GAAG,EAAC;CACd,EAAE;CACF,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,EAAE;CACtB,EAAE,QAAQ,GAAG,KAAK,CAAC;CACnB,EAAE;CACF,CAAC,IAAI,QAAQ,GAAG;CAChB,EAAE,OAAO,QAAQ,CAAC;CAClB,EAAE;CACF,CAAC,eAAe,GAAG;CACnB,EAAE,IAAI,IAAI,CAAC,UAAU;CACrB,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;CACvB,EAAE,IAAI,IAAI,CAAC,YAAY;CACvB,GAAG,IAAI,CAAC,YAAY,GAAG,GAAE;CACzB,EAAE;CACF,EAAC;AACD;CACA,gBAAgB,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,iBAAiB,QAAQ,EAAE,MAAM,GAAE;CACtJ,UAAU,GAAG,CAAC;CACd,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACpC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,KAAI;CACrC,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;CACtG;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAC;CAC1C,GAAG,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;CACnD;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;CAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,WAAW,KAAK,CAAC,CAAC,EAAC;CAC3G,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,OAAO,EAAC;CAC9C,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;CAC7B,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;CAC3B,IAAI,gBAAgB,CAAC,CAAC,EAAC;CACvB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;CACrC,IAAI;CACJ;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM;CACT;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;CAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAE;CAC1B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,EAAC;CACnE,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC;CACpE,GAAG;CACH,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACnC,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;CACvB,GAAG,OAAO,IAAI,CAAC,EAAE,CAAC;CAClB,GAAG;CACH,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC;CAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,KAAK,EAAC;CACb,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;CAClD,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;CACrC,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;CACrC,EAAE,IAAI,IAAI,CAAC,SAAS;CACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;CACtD;CACA,GAAG,WAAW,CAACD,eAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;CACxG,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE;CACpC,EAAE,IAAI,WAAW,GAAG,UAAU,CAAC,YAAW;CAC1C,EAAE,IAAI,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS;CACjD,GAAG,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAC;CACtF;CACA,GAAG,WAAW,CAAC,UAAU,EAAE,gBAAgB,EAAC;CAC5C,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;CACrC,EAAE,IAAI,IAAI,CAAC,SAAS;CACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;CACtD;CACA,GAAG,WAAW,CAACA,eAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;CACxG,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,EAAE;CAC5B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC/C,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;CACzB,EAAE;CACF,CAAC,EAAC;AACF;CACA,SAAS,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE;CACpE,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,WAAU;CACnC,CAAC,IAAI,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE;CACzB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,EAAC;CACjC,EAAE,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,OAAO,EAAE;CAClC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAC;CACxC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,KAAI;CAC1C,EAAE,MAAM;CACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAC;CAC5C,EAAE,QAAQ,IAAI,EAAC;CACf,EAAE;CACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,EAAC;CAChE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAC;CACtG,CAAC;CACD,SAAS,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE;CAC/C,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,WAAU;CAC/B,CAAC,IAAI,MAAM,EAAE,SAAQ;CACrB,CAAC,IAAI,MAAM,GAAG,KAAK,EAAE;CACrB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAC7B,EAAE,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAC9B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CAClC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACpC,EAAE,MAAM;CACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CACxC,EAAE,QAAQ,IAAI,EAAC;CACf,EAAE;CACF,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;CAC7B,CAAC;AACD;CACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC5D,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;CAC3B,CAAC,QAAQ,MAAM;CACf,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE;CACF,GAAG,IAAI,MAAM,GAAG,KAAK,EAAE;CACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAC/B,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,GAAE;CACrC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,IAAI,KAAI;CAC9C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,KAAI;CAC7C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI;CACJ,EAAE;CACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;CAC7B,CAAC,QAAQ,IAAI,OAAM;CACnB,CAAC,OAAO,QAAQ;CAChB,CAAC;AACD;CACA,SAAS,SAAS,CAAC,UAAU,EAAE,WAAW,EAAE;CAC5C;CACA,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,EAAC;CAC5C,CAAC,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,eAAc;CACjD,CAAC,OAAO,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE;CACpC,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;CAC5B,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,GAAE;CACpB,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,cAAc,EAAE,MAAM,EAAE,OAAO,EAAC;CACjE,EAAE,cAAc,IAAI,EAAC;CACrB,EAAE,IAAI,QAAQ,GAAG,MAAM,GAAG,eAAc;CACxC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,GAAE;CACnC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,KAAI;CAC5C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,KAAI;CAC3C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAI;CACpC,EAAE,OAAO,GAAG,OAAM;CAClB,EAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;AACD;CACA,SAAS,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,iBAAiB,EAAE;CACtD,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;CAChC,EAAE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,GAAG,iBAAiB,GAAG,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAC;CACvH,EAAE,cAAc,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;CACpD,EAAE,IAAI,YAAY,GAAG,eAAc;CACnC,EAAE,cAAc,GAAG,KAAI;CACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;CACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;CACvB,EAAE;CACF,CAAC;AACD;CACO,SAASL,cAAY,CAAC,SAAS,EAAE;CACxC,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE;CACtB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;CACzC,GAAG,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC;CAC7D,EAAE,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI;CACvC,GAAG,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC;CACpF,EAAE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAC;CAC3C,EAAE,UAAU,CAAC,OAAO,CAAC,SAAS,EAAC;CAC/B,EAAE;CACF,CAACO,cAAkB,CAAC,SAAS,EAAC;CAC9B,CAAC;CACD,SAASD,mBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE;CAC9C,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,kBAAkB,KAAK;CACnD,EAAE,IAAI,UAAU,GAAG,CAAC,kBAAkB,KAAK,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,EAAC;CAChH,EAAE,IAAI,CAAC,UAAU;CACjB,GAAG,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;CAC9C,EAAE,OAAO,UAAU,CAAC;CACpB,GAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;CACM,SAAS,mBAAmB,CAAC,UAAU,EAAE,cAAc,EAAE;CAChE,CAAC,gBAAgB,GAAG,UAAU,CAAC;CAC/B,CAACA,mBAAiB,GAAG,cAAc,CAAC;CACpC,CAAC;AACD;CACA,IAAI,YAAY,GAAG,IAAIE,OAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;CAC5C,MAAMC,MAAI,GAAG,YAAY,CAAC,KAAI;CACf,YAAY,CAAC,KAAI;CAKhC,MAAM,iBAAiB,GAAG,IAAG;CAC7B,MAAM,iBAAiB,GAAG,KAAI;CAC9B,MAAM,mBAAmB,GAAG;;CC/iCnC,MAAM,KAAK,GAAG,CAAC,CAAC;CAChB,MAAM,MAAM,GAAG,CAAC,CAAC;CACjB,MAAM,IAAI,GAAG,CAAC,CAAC;CACf,MAAM,WAAW,GAAG,CAAC,CAAC;CACtB,MAAM,IAAI,GAAG,EAAE,CAAC;CAChB,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;CACxD,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;CAC1B,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;CAC5E,IAAI,aAAa,CAAC;CAClB,IAAI;CACJ,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;CAClB,CAAC,aAAa,GAAG,IAAI,CAAC;CACtB,CAAC,CAAC,MAAM,KAAK,EAAE;CACf;CACA,CAAC;AACD;CACA,IAAI,eAAe,CAAC;CACpB,MAAM,aAAa,GAAG,OAAO,MAAM,KAAK,YAAW;CACnD,IAAI,WAAW,EAAE,aAAa,CAAC;CAC/B,IAAI;CACJ,CAAC,WAAW,GAAG,IAAI,WAAW,GAAE;CAChC,CAAC,CAAC,OAAO,KAAK,EAAE,EAAE;CAClB,MAAM,UAAU,GAAG,aAAa,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;CACtE,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;CACxE,CAAC,GAAG,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU;CAC1C,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;CACpC,EAAE,OAAO,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;CAC1E,EAAE,GAAG,MAAK;CAIV,mBAAmB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;CACpD,SAAS,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE;CACjG,CAAC,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;CACpE;CACA,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;CAClC,CAAC,IAAI,iBAAiB,GAAG,CAAC,YAAY,CAAC,eAAe,IAAI,GAAG,IAAI,QAAQ,CAAC;CAC1E,CAAC,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;CAClC,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,QAAQ,GAAG,OAAO,EAAE;CACzB,EAAE,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;CAC9B,EAAE,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;CAC/B,EAAE,QAAQ,IAAI,aAAa,CAAC;CAC5B,EAAE,KAAK,IAAI,aAAa,CAAC;CACzB,EAAE,iBAAiB,IAAI,aAAa,CAAC;CACrC,EAAE,aAAa,GAAG,CAAC,CAAC;CACpB,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;CAC/B,EAAE;AACF;CACA,CAAC,IAAI,SAAS,EAAE,WAAW,GAAG,iBAAiB,CAAC;AAChD;CACA,CAAC,IAAI,UAAU,GAAG,YAAY,CAAC,WAAW,KAAK,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CAC/F,CAAC,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;CACzD,CAAC,IAAI,UAAU;CACf,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC;CAClB,GAAG,MAAM,GAAG,IAAI,GAAG,CAAC;CACpB,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC;CACvB,KAAK,MAAM,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;CAC/B,CAAC,IAAI,UAAU,KAAK,CAAC;CACrB,EAAE,OAAO,CAAC,CAAC;CACX,CAAC,QAAQ,IAAI,UAAU,CAAC;CACxB,CAAC,IAAI,gBAAgB,GAAG,EAAE,CAAC;CAC3B,CAAC,IAAI,UAAU,CAAC;CAChB,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC;CAClB,CAAC,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CACzB,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;CAC1B,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;CACvC,EAAE,IAAI,CAAC,cAAc,EAAE;CACvB,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;CACtC,IAAI,GAAG;CACP,IAAI,MAAM,EAAE,UAAU;CACtB,IAAI,iBAAiB,EAAE,CAAC;CACxB,IAAI,MAAM,EAAE,IAAI;CAChB,IAAI,MAAM,EAAE,IAAI;CAChB,IAAI,IAAI,EAAE,IAAI;CACd,IAAI,QAAQ,EAAE,IAAI;CAClB,IAAI,QAAQ,EAAE,IAAI;CAClB,IAAI,KAAK,EAAE,IAAI;CACf,IAAI,OAAO,EAAE,IAAI;CACjB,IAAI,MAAM,EAAE,IAAI;CAChB,IAAI,CAAC;CACL,GAAG;CACH,EAAE,IAAI,QAAQ,GAAG,OAAO,EAAE;CAC1B,GAAG,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;CAC/B,GAAG,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;CAChC,GAAG,QAAQ,IAAI,aAAa,CAAC;CAC7B,GAAG,KAAK,IAAI,aAAa,CAAC;CAC1B,GAAG,iBAAiB,IAAI,aAAa,CAAC;CACtC,GAAG,WAAW,IAAI,aAAa,CAAC;CAChC,GAAG,aAAa,GAAG,CAAC,CAAC;CACrB,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAC/B,GAAG;CACH,EAAE,QAAQ,OAAO,KAAK;CACtB,GAAG,KAAK,QAAQ;CAChB,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;CACvB;CACA,IAAI,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;CAC/C,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,GAAG,UAAU,IAAI,MAAM,GAAG,CAAC,UAAU,EAAE;CAChF,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,cAAc,CAAC,IAAI,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;CACtJ,OAAO,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;CAC3F,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC;CACnC,OAAO,MAAM;CACb,OAAO,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;CAC5F,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;CACpD,OAAO,QAAQ,IAAI,CAAC,CAAC;CACrB,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,MAAM,IAAI,MAAM,GAAG,WAAW,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE;CAC/D,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;CACpD,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;CACtD,OAAO,IAAI,SAAQ;CACnB;CACA,OAAO,IAAI,CAAC,CAAC,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE;CACjI,QAAQ,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;CAC7F,QAAQ,QAAQ,IAAI,CAAC,CAAC;CACtB,QAAQ,MAAM;CACd,QAAQ;CACR,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;CACzF,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;CAClD,IAAI,QAAQ,IAAI,CAAC,CAAC;CAClB,IAAI,MAAM;CACV,GAAG,KAAK,QAAQ;CAChB,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;CACjC,IAAI,SAAS,GAAG,WAAW,GAAG,iBAAiB,CAAC;CAChD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,GAAG,OAAO,EAAE;CAClD,KAAK,MAAM,GAAG,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;CACvD,KAAK,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;CAClC,KAAK,QAAQ,IAAI,aAAa,CAAC;CAC/B,KAAK,KAAK,IAAI,aAAa,CAAC;CAC5B,KAAK,iBAAiB,IAAI,aAAa,CAAC;CACxC,KAAK,WAAW,IAAI,aAAa,CAAC;CAClC,KAAK,aAAa,GAAG,CAAC,CAAC;CACvB,KAAK,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CACjC,KAAK;CACL,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE;CACjD,KAAK,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC;CACzD,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,WAAU;CAClB,IAAI,IAAI,QAAQ,GAAG,WAAW,CAAC;CAC/B,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;CAC1B,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;CACnB,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;CACrC,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;CAC9B,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE;CACrB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAE;CACjC,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;CAC7B,OAAO,UAAU,GAAG,IAAI,CAAC;CACzB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,KAAI;CAC7C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,MAAM;CAC/B,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,MAAM,MAAM;CAC3D,QAAQ;CACR,OAAO,UAAU,GAAG,IAAI,CAAC;CACzB,OAAO,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC;CAC3D,OAAO,CAAC,GAAE;CACV,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAI;CACrD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,UAAU,GAAG,IAAI,CAAC;CACzB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO;CACP,MAAM;CACN,KAAK,MAAM;CACX,KAAK,WAAW,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;CAC3D,KAAK,UAAU,GAAG,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;CACrD,KAAK;CACL,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE;CACrG;CACA,KAAK,IAAI,UAAU,EAAE;CACrB,MAAM,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,EAAE;CAClD,OAAO,IAAI,YAAY,CAAC,MAAM,GAAG,EAAE,KAAK,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE;CAC7E;CACA,QAAQ,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;CACjC,QAAQ,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;CACrC,QAAQ,cAAc,CAAC,OAAO,GAAG,UAAU,CAAC;CAC5C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;CAC5B,QAAQ,MAAM;CACd,QAAQ,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;CACnE,QAAQ;CACR,OAAO;CACP,MAAM,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE;CAChD,MAAM,UAAU,GAAG,IAAI,CAAC;CACxB,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;CAC3F,MAAM,MAAM;CACZ,MAAM;CACN,UAAU,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,GAAG,EAAE,KAAK,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;CAC3H,MAAM,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;CAClE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC;CACpC,KAAK,MAAM;CACX;CACA;CACA,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;CAC5F;CACA;CACA,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;CACrD,KAAK,QAAQ,IAAI,CAAC,CAAC;CACnB,KAAK;CACL,IAAI,MAAM;CACV,GAAG,KAAK,QAAQ;CAChB,IAAI,IAAI,KAAK,EAAE;CACf,KAAK,IAAI,KAAK,CAAC,WAAW,KAAK,IAAI,EAAE;CACrC,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;CAC1F,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;CAC7D,MAAM,QAAQ,IAAI,CAAC,CAAC;CACpB,MAAM,MAAM;CACZ,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;CAClD,MAAM;CACN,KAAK,MAAM;CACX,KAAK,MAAM;CACX,KAAK,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;CACzE,KAAK,IAAI,cAAc,EAAE;CACzB,MAAM,UAAU,GAAG,cAAc,CAAC;CAClC,MAAM,QAAQ,GAAG,eAAe,CAAC;CACjC,MAAM,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;CACxD,KAAK;CACL,IAAI,MAAM;CACV,GAAG,KAAK,SAAS;CACjB,IAAI,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;CACjH,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;CAC7C,IAAI,MAAM;CACV,GAAG,KAAK,WAAW;CACnB,IAAI,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;CACvE,IAAI,IAAI,cAAc,EAAE;CACxB,KAAK,UAAU,GAAG,cAAc,CAAC;CACjC,KAAK,QAAQ,GAAG,eAAe,CAAC;CAChC,KAAK,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;CACvD,IAAI,MAAM;CACV,GAAG;CACH,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;CAChD,GAAG;CACH,EAAE,QAAQ,EAAE,CAAC;CACb,EAAE;AACF;CACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG;CACtD,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CAClC,EAAE,IAAI,KAAK,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CACpC,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CAC5C,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;CACvC,EAAE,IAAI,CAAC,cAAc,EAAE;CACvB,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;CACtC,IAAI,GAAG;CACP,IAAI,MAAM,EAAE,UAAU;CACtB,IAAI,iBAAiB,EAAE,aAAa,GAAG,QAAQ;CAC/C,IAAI,MAAM,EAAE,IAAI;CAChB,IAAI,MAAM,EAAE,IAAI;CAChB,IAAI,IAAI,EAAE,IAAI;CACd,IAAI,QAAQ,EAAE,IAAI;CAClB,IAAI,QAAQ,EAAE,IAAI;CAClB,IAAI,KAAK,EAAE,IAAI;CACf,IAAI,OAAO,EAAE,IAAI;CACjB,IAAI,CAAC;CACL,GAAG;CACH,EAAE,IAAI,WAAW,CAAC;CAClB,EAAE,IAAI,KAAK,EAAE;CACb;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAG,IAAI,IAAI,CAAC;CACZ,GAAG,SAAS,GAAG,WAAW,GAAG,iBAAiB,CAAC;CAC/C,GAAG,IAAI,SAAS,GAAG,MAAM,EAAE;CAC3B,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;CACzC,IAAI,IAAI,UAAU;CAClB,KAAK,IAAI,GAAG,CAAC,CAAC;CACd,SAAS,KAAK,UAAU,GAAG,cAAc,CAAC,QAAQ;CAClD,KAAK,IAAI,GAAG,CAAC,CAAC;CACd,SAAS;CACT,KAAK,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;CACvE,KAAK,IAAI,GAAG,CAAC,CAAC;CACd,KAAK;CACL,IAAI,MAAM;CACV,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;CACjG,IAAI,IAAI,GAAG,CAAC,CAAC;CACb,IAAI;CACJ,GAAG,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;CAC1C;CACA,GAAG,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;CACxC;CACA,IAAI,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC;CACvC,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;CACxC,IAAI,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;CAChC,IAAI,iBAAiB,IAAI,aAAa,CAAC;CACvC,IAAI,QAAQ,IAAI,aAAa,CAAC;CAC9B,IAAI,KAAK,IAAI,aAAa,CAAC;CAC3B,IAAI,aAAa,GAAG,CAAC,CAAC;CACtB,IAAI;CACJ,IAAI,WAAW,GAAG,WAAW,CAAC;CAC9B,GAAG,IAAI,IAAI,KAAK,CAAC,EAAE;CACnB,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;CACpD,IAAI,QAAQ,IAAI,CAAC,CAAC;CAClB,IAAI,MAAM;CACV,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;CACpD,IAAI,QAAQ,IAAI,CAAC,CAAC;CAClB,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;CAChG,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;CAClE,GAAG,QAAQ,IAAI,CAAC,CAAC;CACjB,GAAG;CACH,EAAE,QAAQ,EAAE,CAAC;CACb,EAAE;AACF;AACA;CACA,CAAC,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;CAC1C,CAAC,IAAI,QAAQ,IAAI,IAAI,EAAE;CACvB,EAAE,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;CACvC,EAAE,IAAI,SAAS,GAAG,EAAE,CAAC;CACrB,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC;CAClC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;CAChB,EAAE,OAAO,CAAC,IAAI,GAAG,cAAc,CAAC,MAAM,MAAM,SAAS,EAAE;CACvD,GAAG,IAAI,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC;CACpC,GAAG,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC;CAC5C,GAAG,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;CAC5B,GAAG,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;CACpC,GAAG,IAAI,cAAc,CAAC,iBAAiB;CACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;CACpD,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;CAC5B,GAAG,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC;CAC1C,GAAG;CACH,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;CACtB,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;CACvC,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;CAC3C,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;CACtB,EAAE;AACF;AACA;CACA,CAAC,QAAQ,UAAU;CACnB,EAAE,KAAK,CAAC;CACR,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;CAClC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;CACnC,GAAG,MAAM;CACT,EAAE,KAAK,CAAC;CACR,GAAG,IAAI,QAAQ,IAAI,KAAK,EAAE,OAAO,CAAC,CAAC;CACnC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;CACxB,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;CAChC,GAAG,MAAM;CACT,EAAE,KAAK,CAAC;CACR,GAAG,IAAI,QAAQ,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC;CACrC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;CACxB,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;CACnD,GAAG,MAAM;CACT,EAAE,KAAK,CAAC;CACR,GAAG,IAAI,QAAQ,IAAI,SAAS,EAAE,OAAO,CAAC,CAAC;CACvC,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC;CAC7D,GAAG,MAAM;CACT,EAAE;AACF;CACA,CAAC,IAAI,QAAQ,GAAG,iBAAiB,EAAE;CACnC,EAAE,IAAI,iBAAiB,KAAK,WAAW;CACvC,GAAG,OAAO,QAAQ,CAAC;CACnB;CACA,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;CAC9D,EAAE,WAAW,IAAI,QAAQ,GAAG,iBAAiB,CAAC;CAC9C,EAAE,YAAY,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;CAClD,EAAE,MAAM,IAAI,QAAQ,GAAG,iBAAiB,EAAE;CAC1C,EAAE,IAAI,iBAAiB,KAAK,WAAW;CACvC,GAAG,OAAO,QAAQ,CAAC;CACnB,EAAE,YAAY,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;CAClD,EAAE,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;CAC9F,EAAE;CACF,CAAC,OAAO,WAAW,CAAC;CACpB,CAAC;CACD,SAAS,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;CAC1D,CAAC,IAAI,cAAc,CAAC;CACpB,CAAC,KAAK,cAAc,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,GAAG;CAC9D,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;CAC5C,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;CACjC,EAAE,OAAO,cAAc,CAAC;CACxB,EAAE;CACF,CAAC,KAAK,cAAc,GAAG,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,GAAG;CACpE,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;CAC7C,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;CACjC,EAAE,OAAO,cAAc,CAAC;CACxB,EAAE;CACF,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,KAAK,EAAE;CACxC,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;CAC3D,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;CACjC,EAAE,OAAO,cAAc,CAAC;CACxB,EAAE;CACF;CACA,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,KAAK,EAAE;CACxC,EAAE,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;CAC7C,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;CACtC,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;CACjC,EAAE,OAAO,cAAc,CAAC;CACxB,EAAE;CACF,CAAC,eAAe,GAAG,QAAQ,CAAC;CAC5B;CACA,CAAC,OAAO;CACR,CAAC;CACD,SAAS,oBAAoB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE;CACtD,CAAC,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;CAC/C,CAAC,IAAI,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1F,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;CAC7B,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;CAC7B,CAAC,aAAa,CAAC,QAAQ,GAAG,UAAU,CAAC;CACrC,CAAC,OAAO,aAAa,CAAC;CACtB,CAAC;CACD,SAAS,kBAAkB,CAAC,UAAU,EAAE;CACxC,CAAC,IAAI,EAAE,UAAU,YAAY,GAAG,CAAC;CACjC,EAAE,OAAO,UAAU,CAAC;CACpB,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAC3C,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;CAC3B,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;CACrC,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CACvC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC/C,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;CAC3B,EAAE,IAAI,UAAU,GAAG,WAAW,CAAC;CAC/B,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,SAAS,EAAE;CAC3C,GAAG,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;CACxC,GAAG,IAAI,CAAC,cAAc,EAAE;CACxB,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;CACvC,KAAK,GAAG;CACR,KAAK,MAAM,EAAE,UAAU;CACvB,KAAK,iBAAiB,EAAE,CAAC;CACzB,KAAK,MAAM,EAAE,IAAI;CACjB,KAAK,MAAM,EAAE,IAAI;CACjB,KAAK,IAAI,EAAE,IAAI;CACf,KAAK,QAAQ,EAAE,IAAI;CACnB,KAAK,QAAQ,EAAE,IAAI;CACnB,KAAK,KAAK,EAAE,IAAI;CAChB,KAAK,OAAO,EAAE,IAAI;CAClB,KAAK,MAAM,EAAE,IAAI;CACjB,KAAK,CAAC;CACN,IAAI;CACJ,GAAG,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;CACjE,GAAG;CACH,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;CAChC,EAAE;CACF,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;CACjC,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;CAC3B,CAAC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,MAAM,CAAC;CAC/C,CAAC,OAAO,KAAK,CAAC;CACd,CAAC;CACD,IAAI,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAC;CACvC,SAAS,UAAU,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;CACpD,CAAC,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;CACvC,CAAC,IAAI,QAAQ,IAAI,EAAE,EAAE;CACrB,EAAE,OAAO,QAAQ;CACjB,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM;CAC9C;CACA,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;CACvE,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM;CACjG,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM;CAC3H,GAAG;CACH,EAAE;CACF,CAAC,IAAI,SAAS,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;CACxE,CAAC,IAAI,CAAC,SAAS,EAAE;CACjB;CACA,EAAE,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;CAC/D,EAAE,MAAM,IAAI,QAAQ,CAAC;CACrB,EAAE,QAAQ,GAAG,CAAC,CAAC;CACf,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;CAC5B,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,QAAQ,CAAC,6BAA6B,CAAC,CAAC,CAAC;CAC7F,EAAE,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;CACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;CAC3B,GAAG,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;CACjE,EAAE,OAAO,CAAC,yBAAyB,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;CAClE,EAAE,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;CAC7C,EAAE,IAAI,CAAC,SAAS;CAChB,GAAG,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,QAAQ,CAAC,CAAC;CACjE,EAAE;CACF,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;CACrC,CAAC,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;CAC7C,CAAC,IAAI,CAAC,SAAS,EAAE;CACjB,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,GAAG;CAC1D,IAAG;CACH,EAAE,aAAa,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,YAAY,GAAG;CACpE,IAAG;CACH,EAAE,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;CAC1D,EAAE,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;CAC9G,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;CACtB,EAAE,IAAI,aAAa,GAAG,CAAC,CAAC;CACxB,EAAE,IAAI,eAAe,CAAC;CACtB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACpD,GAAG,IAAI,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACjC,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAC;CAC3D,GAAG,IAAI,GAAG,KAAK,WAAW;CAC1B,IAAI,GAAG,GAAG,UAAU,CAAC;CACrB,GAAG,IAAI,QAAQ,GAAG;CAClB,IAAI,GAAG;CACP,IAAI,MAAM,EAAE,aAAa;CACzB,KAAI;CACJ,GAAG,IAAI,iBAAiB;CACxB,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;CAC1D;CACA,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;CAC9B,GAAG,IAAI,MAAM,CAAC;CACd,GAAG,OAAO,IAAI;CACd,IAAI,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM;CACpC,IAAI,KAAK,CAAC;CACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;CACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CACzD,MAAM,OAAO,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;CACjD,MAAM,CAAC;CACP,KAAK,MAAM;CACX,IAAI,KAAK,CAAC;CACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;CACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;CAC/G,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;CACrE,MAAM,OAAO,GAAG,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;CAC1D,MAAM,CAAC;CACP,KAAK,MAAM;CACX,IAAI,KAAK,CAAC;CACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;CACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;CAC/G,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;CACrE,MAAM,OAAO,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;CAC9D,MAAM,CAAC;CACP,KAAK,MAAM;CACX,IAAI;CACJ,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;CAC5B,GAAG,aAAa,IAAI,IAAI,CAAC;CACzB,GAAG,IAAI,GAAG,CAAC;CACX,GAAG,OAAO,IAAI;CACd,IAAI,KAAK,KAAK;CACd,KAAK,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI;CACjD,MAAM,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;CACtC,KAAK,eAAe,GAAG,QAAQ,CAAC;CAChC,KAAK,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC;CAChC,KAAK,GAAG,GAAG,SAAS,MAAM,EAAE;CAC5B,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,MAAM,IAAI,QAAQ,GAAG,aAAa,GAAG,QAAQ,CAAC;CAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CACzC,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,GAAG,CAAC;AAC9C;CACA,MAAM,IAAI,GAAG,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;CACpC,MAAM,MAAM,IAAI,EAAE;CAClB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CAC3C,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ;CAClC,QAAQ,MAAM;CACd;CACA,QAAQ,GAAG,GAAG,IAAI,CAAC;CACnB,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACxB,OAAO;CACP,MAAM,IAAI,GAAG,IAAI,IAAI;CACrB,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;CACxC,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;CAC5B,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;CAC/C,OAAO;CACP;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;CACxD;CACA,MAAM,CAAC;CACP,KAAK,MAAM;CACX,IAAI,KAAK,IAAI,CAAC,CAAC,KAAK,WAAW;CAC/B,KAAK,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI;CACjD,MAAM,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;CACtC,KAAK,eAAe,GAAG,QAAQ,CAAC;CAChC,KAAK,GAAG,GAAG,SAAS,MAAM,EAAE;CAC5B,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,MAAM,IAAI,QAAQ,GAAG,aAAa,GAAG,QAAQ,CAAC;CAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CACzC,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,GAAG,CAAC;CAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,MAAM,IAAI,GAAG,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;CACpC,MAAM,MAAM,IAAI,EAAE;CAClB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CAC3C,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ;CAClC,QAAQ,MAAM;CACd;CACA,QAAQ,GAAG,GAAG,IAAI,CAAC;CACnB,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACxB,OAAO;CACP,MAAM,IAAI,GAAG,IAAI,IAAI;CACrB,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;CACxC,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;CACzB,OAAO,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC;CACnE,OAAO,MAAM;CACb,OAAO,aAAa,GAAG,MAAM,CAAC;CAC9B,OAAO,IAAI;CACX,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;CACnF,QAAQ,SAAS;CACjB,QAAQ,aAAa,GAAG,IAAI,CAAC;CAC7B,QAAQ;CACR,OAAO;CACP,MAAM,CAAC;CACP,KAAK,MAAM;CACX,IAAI,KAAK,MAAM;CACf,KAAK,OAAO,IAAI;CAChB,MAAM,KAAK,CAAC;CACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;CAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC/B,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;CACjH,QAAQ,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;CACzD,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAC;CACrD,QAAQ,IAAI,KAAK,GAAG,UAAU,EAAE;CAChC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;CAChC,UAAU,OAAO,KAAK,CAAC;CACvB,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;CAChC,UAAU,OAAO,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;CAC1C,SAAS;CACT,QAAQ,IAAI,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;CACzD;CACA,QAAQ,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CAC7F,QAAQ,OAAO,CAAC,CAAC,UAAU,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC;CACrF,QAAQ,CAAC;CACT,OAAO,MAAM;CACb,MAAM,KAAK,CAAC;CACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;CAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC/B,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;CACjH,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;CACjF,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;CAC1B,SAAS,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3D,SAAS,IAAI,IAAI,IAAI,IAAI;CACzB,UAAU,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;CAClC,SAAS;CACT,QAAQ,OAAO,KAAK,CAAC;CACrB,QAAQ,CAAC;CACT,OAAO,MAAM;CACb,MAAM,KAAK,CAAC;CACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;CAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC/B,QAAQ,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3D,QAAQ,OAAO,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;CACxD,QAAQ,CAAC;CACT,OAAO,MAAM;CACb,MAAM;CACN,KAAK,MAAM;CACX,IAAI,KAAK,IAAI;CACb,KAAK,GAAG,GAAG,UAAU,MAAM,EAAE;CAC7B,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;CAC/G,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;CACpF,MAAM,CAAC;CACP,KAAK,MAAM;AACX;CACA,IAAI;CACJ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;CACtB,GAAG;CACH;CACA,EAAE,IAAI,aAAa,EAAE;CACrB,GAAG,IAAI,uBAAuB,GAAG,EAAE,CAAC;CACpC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC;CACjB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;CACb,GAAG,IAAI,sBAAsB,CAAC;CAC9B,GAAG,KAAK,IAAI,QAAQ,IAAI,UAAU,EAAE;CACpC,IAAI,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;CAChF;CACA;CACA,KAAK,sBAAsB,GAAG,IAAI,CAAC;CACnC,KAAK,SAAS;CACd,KAAK;CACL,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;CACxG,IAAI,IAAI,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;CAClC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CAC7B,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC;CACrG,IAAI;CACJ,GAAG,IAAI,sBAAsB,EAAE;CAC/B,IAAI,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;CACnD,IAAI;CACJ,GAAG,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAI,EAAE,8CAA8C,GAAG,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,EAAE,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;CACrM,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;CAC9C,IAAI,KAAK,CAAC,yBAAyB,EAAE;CACrC,KAAK,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;CACpD,KAAK;CACL,IAAI,CAAC,CAAC;CACN,GAAG,MAAM;CACT,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;CAC9C,IAAI,KAAK,CAAC,yBAAyB,EAAE;CACrC;CACA,KAAK,IAAI,QAAQ,GAAG,EAAE,CAAC;CACvB,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACxD;CACA,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClC;CACA,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;CAChC,MAAM;CACN,KAAK,OAAO,QAAQ,CAAC;CACrB,KAAK;CACL;CACA,IAAI,CAAC,CAAC;CACN,GAAG;CACH,EAAE;CACF,CAAC,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;CAChC,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG;CAC1B,EAAE,KAAK,EAAE,GAAG;CACZ,EAAE,QAAQ;CACV,EAAE,SAAS,EAAE,EAAE;CACf,EAAE,QAAQ,EAAE,MAAM;CAClB,GAAE;CACF,CAAC,OAAO,QAAQ,CAAC;CACjB,CAAC;CACD,SAAS,UAAU,CAAC,IAAI,EAAE;CAC1B,CAAC,OAAO,IAAI;CACZ,EAAE,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;CACzB,EAAE,KAAK,IAAI,EAAE,OAAO,SAAS,CAAC;CAC9B,EAAE,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC;CAC1B,EAAE,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;CACzB,EAAE;CACF,CAAC,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;CACrC,CAAC;CACD,SAAS,UAAU,CAAC,GAAG,EAAE;CACzB,CAAC,OAAO,WAAW;CACnB,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;CACjC,EAAE;CACF,CAAC;AACD;CACA,SAAS,SAAS,GAAG;CACrB,CAAC,IAAI,aAAa,EAAE;CACpB,EAAE,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;CAC7H,EAAE,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC;CAC7B,EAAE,aAAa,CAAC,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;CACtD,EAAE;CACF,CAAC;CACD,SAAS,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE;CAC9C,CAAC,IAAI,KAAK,CAAC,YAAY,EAAE;CACzB,EAAE,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;CAC5B,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;CACrC,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;CAC7C,EAAE,UAAU,GAAG,SAAS,CAAC;CACzB,EAAE;CACF,CAAC,IAAI,yBAAyB,GAAG,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;CACtE,CAAC,UAAU,CAAC,YAAY,GAAG,QAAQ,IAAI;CACvC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;CACxB,EAAE,IAAI,QAAQ,YAAY,GAAG,EAAE;CAC/B,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAC3C,GAAG,IAAI,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;CAC9D,IAAI,UAAU,GAAG,KAAK,CAAC;CACvB,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAC3C,GAAG,IAAI,KAAK,CAAC,MAAM,KAAK,yBAAyB;CACjD,IAAI,UAAU,GAAG,KAAK,CAAC;CACvB,GAAG,MAAM,IAAI,QAAQ,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;CACnE,GAAG,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;CACjE,IAAI,UAAU,GAAG,KAAK,CAAC;CACvB,GAAG;CACH,EAAE,IAAI,CAAC,UAAU;CACjB,GAAG,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;CACpC,EAAE,OAAO,UAAU,CAAC;CACpB,EAAE,CAAC;CACH,CAAC,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;CACnF,CAAC,OAAO,UAAU,CAAC;CACnB,CAAC;AACD;CACA,aAAa,CAAC,UAAU,EAAE,kBAAkB,EAAE,SAAS,CAAC;;CCnyBxD,MAAM,0BAA0B,GAAG,OAAO,CAAC,GAAG,CAAC,qCAAqC,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACjL;CACA,IAAI,CAAC,0BAA0B,EAAE;CACjC,CAAC,IAAI,UAAS;CACd,CAAC,IAAI;CACL,EAAE,IAAI,OAAO,OAAO,IAAI,UAAU;CAClC,GAAG,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAC;CAC1C;CACA,GAAG,SAAS,GAAGC,oBAAa,CAAC,mGAAe,CAAC,CAAC,kBAAkB,EAAC;CACjE,EAAE,IAAI,SAAS;CACf,GAAG,YAAY,CAAC,SAAS,CAAC,cAAc,EAAC;CACzC,EAAE,CAAC,OAAO,KAAK,EAAE;CACjB;CACA,EAAE;CACF;;CCnBA,IAAI,aAAa,GAAG,EAAE,CAAC;CACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC5B,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAACC,eAAY,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,mGAAe,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3G,CAAC;CACD,aAAa,CAAC,IAAI,CAAC;CACnB,CAAC,IAAI,EAAE,kBAAkB;CACzB,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE;CACjB,CAAC,KAAK,EAAE,EAAE;CACV,CAAC,EAAC;CACF,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CACpC,SAAS,UAAU,CAAC,MAAM,EAAE;CAC5B,CAAC,IAAI;CACL,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC;CACxB,EAAE,CAAC,MAAM,KAAK,EAAE;CAChB,EAAE,OAAO,EAAE;CACX,EAAE;CACF,CAAC;AACD;CACA,IAAI,IAAI,GAAG,CAAC,CAAC;CACb,SAAS,MAAM,GAAG;CAClB,CAAC,IAAI,EAAE,CAAC;CACR,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC;CACzB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,IAAI,UAAU,CAAC;CAC9C,CAAC;CACD;CACA,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;CACxB;CACA,IAAI,KAAK,GAAGC,QAAc;CAC1B,IAAI,OAAO,GAAGC,UAAgB;CAC9B,IAAI,MAAM,GAAGC,SAAe;CAC5B,IAAI,cAAc,GAAGC,iBAAuB;CAC5C,IAAI,YAAY,GAAGC,eAAqB;CACxC,IAAI,IAAI,GAAGC,OAAa;CACxB,IAAI,WAAW,GAAGC,eAAwB,CAAC,YAAW;AACtD;CACA,IAAI,YAAY,GAAGC,eAAqB;CACxC,IAAI,IAAI,GAAG,UAAU,CAAC,MAAM,EAAC;CACX,IAAI,CAAC,YAAW;CAChB,IAAI,CAAC,YAAW;CAChB,IAAI,CAAC,mBAAkB;CACvB,IAAI,CAAC,qBAAoB;CAC3B,IAAI,CAAC,UAAS;AAI9B;CACA,IAAI,UAAU,GAAG,KAAI;AACrB;CACA,MAAM,WAAW,SAAS,KAAK,CAAC;CAChC,CAAC;AACD;CACA,MAAM,YAAY,SAAS,KAAK,CAAC;CACjC,CAAC;AACD;CACA,MAAM,YAAY,SAAS,KAAK,CAAC;CACjC,CAAC;AACD;AACA;CACA,MAAM,YAAY,CAAC;CACnB,CAAC;AACD;AACA;CACA,KAAK,CAAC,sBAAsB,EAAE,WAAW;CACzC,CAAC,IAAI,CAAC,kBAAkB,EAAE,YAAY;CACtC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,IAAI,EAAE;CACT,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;CACjD,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;CACrC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;CACnD,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;CACtC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;CAClD,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;CAClD,IAAI;CACJ,GAAG,WAAW,EAAE,YAAY;CAC5B,GAAG,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;CACzB,GAAG,gBAAgB,EAAE;CACrB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;CAClB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;CAClB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;CAClB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CACb,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CACb,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC;CACvB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;CAChB,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,UAAU,CAAC,EAAC;CACrC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CAC/B,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CAC/B,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,kBAAkB,EAAE,YAAY;CACtC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;CAChC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;CAChC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;CAChC,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,UAAU,CAAC,EAAC;CACrC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,EAAC;CACvC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,EAAC;CACvC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,EAAC;CACvC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY;CACjC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK;CACR,GAAG,KAAK;CACR,GAAG,KAAK;CACR,GAAG,EAAE;CACL,GAAG,EAAE;CACL,GAAG,IAAI;CACP,GAAG,IAAI;CACP,GAAG,OAAO;CACV,GAAG,OAAO;CACV,GAAG,KAAK,EAAE;CACV,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;CACrC,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,UAAU,CAAC,EAAC;CACrC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY;CAC/B,EAAE,MAAM,IAAI,GAAG,kQAAiQ;CAChR,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,EAAC;CAClC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY;CACrC,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;CAChC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;CAC9C,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;CAC9B,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;CACrB,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,EAAE,EAAC;CAC/B,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,wDAAwD,EAAE,YAAY;CAC5E,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,UAAU,EAAE;CAC3G,IAAI,EAAE,aAAa,GAAG;CACtB,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;CACjC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;CACjC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC;CACjB,GAAG,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACjE;CACA,GAAG,SAAS,UAAU,GAAG;CACzB,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;CACjB,IAAI,OAAO,MAAM,EAAE,GAAG,GAAG,EAAE;CAC3B,KAAK,GAAG,IAAI,MAAM,EAAE,GAAG,GAAG,GAAG,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;CACtE,KAAK;CACL,IAAI,OAAO,GAAG,CAAC;CACf,IAAI;AACJ;CACA,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;CACzC,KAAK,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC;CACpD,MAAM,MAAM,EAAE,GAAG,GAAG,GAAG,UAAU,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM,EAAE,GAAG,GAAG,GAAG,UAAU,GAAG,IAAI,CAAC;CACjG,IAAI;CACJ,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACpC,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;CAC/C,GAAG,KAAK,IAAI,GAAG,IAAI,YAAY,EAAE;CACjC,IAAY,YAAY,CAAC,GAAG,EAAE;CAC9B,IAAI;CACJ,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG;CACH,EAAE,EAAC;AACH;CACA,CAAC,KAAK,IAAI,UAAU,IAAI,aAAa,EAAE;CACvC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;CAChE,EAAE,IAAI,CAAC,0BAA0B,GAAG,OAAO,EAAE,YAAY;CACzD,GAAG,IAAI,IAAI,GAAG,WAAU;CAExB,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC9B,GAAG,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACxC,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC9B,GAAG,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACxC,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG,EAAC;CACJ,EAAE,IAAI,CAAC,kDAAkD,GAAG,OAAO,EAAE,YAAY;CACjF,GAAG,IAAI,IAAI,GAAG,WAAU;CAExB,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC9B,GAAG,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,EAAC;CAC1C,GAAG,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACxC,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC9B,GAAG,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACxC,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG,EAAC;CACJ,EAAE,IAAI,CAAC,wDAAwD,GAAG,OAAO,EAAE,YAAY;CACvF,GAAG,IAAI,IAAI,GAAG,WAAU;CACxB,GAAG,IAAI,UAAU,GAAG,GAAE;CACtB,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACzB,IAAI,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,UAAU,EAAE;CAC5G,KAAK,EAAE,aAAa,GAAG;CACvB,KAAK,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;CAClC,KAAK;CACL,IAAI,EAAC;CACL,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAChC,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACrC,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;CAC9D,IAAI,IAAI,MAAM,GAAG,GAAE;CACnB,IAAI,KAAK,IAAI,GAAG,IAAI,YAAY,EAAE;CAClC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;CACrC,KAAK;CACL,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAC;CAClC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,CAAC,+CAA+C,GAAG,OAAO,EAAE,YAAY;CAC9E,GAAG,IAAI,IAAI,GAAG,WAAU;CACxB,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,EAAC;CACnF,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACpC,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC9C,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,CAAC,kDAAkD,EAAE,YAAY;CACtE,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACzC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACzC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACzC,IAAG;CACH;CACA,EAAE,IAAI,YAAY,GAAG;CACrB,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC;CACtB,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;CACrB,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;CACjD,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;CACxE,IAAG;AACH;CACA,EAAE,IAAI,IAAI,CAAC,IAAI,YAAY,EAAE;CAC7B,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAC;CAC3B,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACpC,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC9C,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACvC,GAAG;CACH,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,8BAA8B,EAAE,YAAY;CAClD,EAAE,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,oBAAoB,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,GAAG,CAAC,CAAC;AACxJ;CACA,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;CACxB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAClB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAClB,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;CACxB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACf,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACf,EAAE,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC7B;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACtC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CAC3C,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;CACrC,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;CACrC,EAAE,CAAC,CAAC;CACJ,CAAC,IAAI,CAAC,oCAAoC,EAAE,YAAY;CACxD,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAC;CACjD,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;CAChG,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,6CAA6C,EAAE,YAAY;CACjE,EAAE,IAAI,IAAI,GAAG,GAAE;CACf,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,EAAC;CAC9C,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,gDAAgD,EAAE,WAAW;CACnE,EAAE,MAAM,eAAe,GAAG,CAAC,aAAa,EAAE,IAAI,EAAC;AAC/C;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,OAAO,EAAE,oBAAoB;CAChC,IAAG;AACH;CACA,EAAE,QAAQ,CAAC,GAAG,EAAC;CACf,EAAE,QAAQ,CAAC,IAAI,EAAC;CAChB,EAAE,QAAQ,CAAC,KAAK,EAAC;CACjB,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE;CAC1B,GAAG,MAAM,IAAI,GAAG,GAAE;CAClB,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;CAClC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAC;CACxB,IAAI;AACJ;CACA,GAAG,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,eAAe,EAAC;CAC5C,GAAG,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAC,eAAe,EAAC;CAChD,GAAG,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC;CACpC,GAAG,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAC;CAC3C,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;CACnC,GAAG;CACH,EAAE,CAAC,CAAC;CACJ,CAAC,IAAI,CAAC,6BAA6B,EAAE,YAAY;CACjD,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAC;CAC7B,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,EAAC;CAC3C,EAAE,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,EAAC;CAC/C,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,EAAC;CAC/C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,EAAC;CAC/C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,iDAAiD,EAAE,YAAY;CACrE,EAAE,MAAM,OAAO,GAAG;CAClB,GAAG,aAAa,EAAE,IAAI;CACtB,GAAG,UAAU,EAAE,IAAI;CACnB,GAAG,CAAC;AACJ;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;CACpC,EAAE,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACtC;CACA,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC1B;CACA,EAAE,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC;CACxC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;CACpB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;CACpB,GAAG,CAAC,CAAC,CAAC;CACN,EAAE,CAAC,CAAC;CACJ,CAAC,IAAI,OAAO,MAAM,IAAI,WAAW;CACjC,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU;CAChC,EAAE,IAAI,KAAK,GAAG;CACd,GAAG,IAAI,EAAE;CACT,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,EAAE;CACvE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,aAAa,EAAE;CACzD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,cAAc,EAAE;CACxE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,CAAC;CACvD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,gBAAgB,EAAE;CACzE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;CACpD,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,KAAK,GAAG;CACd,GAAG,IAAI,EAAE;CACT,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACrD,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;CACxC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACvD,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;CAC1C,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACvD,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;CACvD,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,EAAC;CAC/B,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,EAAC;CAC/B,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAC;CAC5B,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,EAAC;CACrB,EAAE,IAAI,aAAa,GAAG,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,EAAC;CACnD,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,EAAC;CACrB,EAAE,IAAI,aAAa,GAAG,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,EAAC;CACnD,EAAE,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAC;CACxC,EAAE,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAC;CACxC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW;CAChC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAC;CACnC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW;CAC3B,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAC;CACnD,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,6BAA6B;CACnC,GAAG,CAAC,EAAE,yCAAyC;CAC/C,GAAG,CAAC,EAAE,CAAC,6BAA6B;CACpC,GAAG,CAAC,EAAE,CAAC,8CAA8C;CACrD,GAAG,CAAC,EAAE,6BAA6B;CACnC,GAAG,CAAC,EAAE,CAAC,6BAA6B;CACpC,GAAG,CAAC,EAAE,CAAC,qBAAqB;CAC5B,GAAG,KAAK,EAAE,EAAE;CACZ,IAAG;CACH;CACA,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,EAAC;AACH;AACA;CACA,CAAC,IAAI,CAAC,4BAA4B,EAAE,UAAU;CAC9C,EAAE,SAAS,QAAQ,GAAG;AACtB;CACA,GAAG;CACH,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW;CAC5C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,IAAG;CACH,EAAE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAE;CAC/B,EAAE,QAAQ,CAAC,KAAK,GAAG,EAAC;CACpB,EAAE,QAAQ,CAAC,MAAM,GAAG,iBAAgB;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,MAAM,EAAE,SAAS,MAAM,EAAE;CAC5B,IAAI,IAAI,CAAC,GAAG,IAAI,QAAQ,GAAE;CAC1B,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAC;CACnC,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,EAAC;CACrB,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,EAAC;CACtB,IAAI,OAAO,CAAC;CACZ,IAAI;CACJ,GAAG,IAAI,EAAE,SAAS,QAAQ,EAAE;CAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;CACxD,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,CAAC,EAAC;CAC5D,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,iCAAiC,EAAE,UAAU;CACnD,EAAE,IAAI,QAAQ,GAAG,IAAI,WAAW,GAAE;CAClC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,WAAW;CACrB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE;CACxB,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,EAAC;CACtD,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG,KAAK,EAAE,SAAS,QAAQ,EAAE;CAC7B,IAAI,OAAO,CAAC,GAAG,QAAQ,CAAC;CACxB,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,WAAW,CAAC,SAAS,EAAC;CACjG,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,8CAA8C,EAAE,UAAU;CAChE,EAAE,IAAI,QAAQ,GAAG,IAAI,YAAY,GAAE;CACnC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnB,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,KAAK,CAAC,SAAS,EAAC;CAC3F,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,EAAC;AACH;AACA;CACA,CAAC,IAAI,CAAC,+CAA+C,EAAE,UAAU;CACjE,EAAE,IAAI,QAAQ,GAAG,IAAI,YAAY,GAAE;CACnC,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;CAC5B,EAAE,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC;CAC3B,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,SAAS,EAAC;CAC5F,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,wCAAwC,EAAE,UAAU;CAC1D,EAAE,SAAS,SAAS,GAAG;AACvB;CACA,GAAG;CACH,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,SAAS;CACnB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG,IAAI,GAAG;CACV,IAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC;CACjF,IAAI;CACJ,GAAG,MAAM,CAAC,IAAI,EAAE;CAChB,IAAI,OAAO,IAAI,CAAC,MAAM;CACtB,IAAI;CACJ,GAAG,CAAC,CAAC;CACL,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC;CAC7C,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,EAAC;CAC3B,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,2BAA2B,EAAE,UAAU;CAC7C,EAAE,SAAS,QAAQ,GAAG;AACtB;CACA,GAAG;CACH,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW;CAC5C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,IAAG;CACH,EAAE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAE;CAC/B,EAAE,QAAQ,CAAC,KAAK,GAAG,EAAC;CACpB,EAAE,QAAQ,CAAC,MAAM,GAAG,iBAAgB;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE;CACxB,IAAI,IAAI,CAAC,GAAG,IAAI,QAAQ,GAAE;CAC1B,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,EAAC;CACrB,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,EAAC;CACtB,IAAI,OAAO,CAAC;CACZ,IAAI;CACJ,GAAG,KAAK,EAAE,SAAS,QAAQ,EAAE;CAC7B,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;CAC5C,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,CAAC,EAAC;CAC5D,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,4BAA4B,EAAE,UAAU;CAC9C,EAAE,SAAS,QAAQ,GAAG;AACtB;CACA,GAAG;CACH,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW;CAC5C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,IAAG;CACH,EAAE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAE;CAC/B,EAAE,QAAQ,CAAC,KAAK,GAAG,EAAC;CACpB,EAAE,QAAQ,CAAC,MAAM,GAAG,iBAAgB;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE;CACxB,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAC;CACnD,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG,KAAK,EAAE,SAAS,IAAI,EAAE;CACzB,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAC;CAC9F,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,CAAC,EAAC;CAC5D,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,kCAAkC,EAAE,UAAU;CACpD,EAAE,IAAI,QAAQ,GAAG,IAAI,YAAY,GAAE;CACnC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC;CAClB,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAC;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,mBAAmB;CAC7B,GAAG,gBAAgB,EAAE,QAAQ;CAC7B,GAAG,KAAK,EAAE,aAAa;CACvB,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,YAAY;CACtB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE;CACxB,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,EAAC;CACvD,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG,KAAK,EAAE,SAAS,IAAI,EAAE;CACzB,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAC;CACtC,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC,SAAS,EAAC;CAClG,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CACnD,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,oCAAoC,EAAE,UAAU;CACtD,EAAE,SAAS,QAAQ,GAAG;CACtB;CACA,GAAG;CACH,EAAE,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU;CAC1C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,IAAG;CACH,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW;CAC5C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,IAAG;AACH;CACA,EAAE,IAAI,QAAQ,GAAG,WAAW,EAAE,QAAQ,CAAC,QAAQ,GAAE,uBAAsB;CACvE,EAAE,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;CACrD;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,EAAC;CACpB,EAAE,IAAI,IAAI,GAAG,SAAQ;AACrB;CACA,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,IAAI,EAAE,EAAE;CACX,GAAG,MAAM,EAAE,SAAS,MAAM,EAAE;CAC5B,IAAI,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,QAAQ,GAAE,GAAE;CACvC,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;CAChD,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAC;CAClC,IAAI,OAAO,CAAC;CACZ,IAAI;CACJ,GAAG,IAAI,EAAE,SAAS,QAAQ,EAAE;CAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;CACrC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC,EAAC;CAC3C,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU;CAC/C,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,KAAK,EAAE,IAAI,IAAI,EAAE;CACpB,IAAG;CACH,EAAc,IAAI,KAAK,GAAE;CACzB,EAAE,YAAY,CAAC;CACf,GAAG,KAAK,EAAE,IAAI;CACd,GAAG,KAAK,CAAC,IAAI,EAAE;CACf,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE;CAC1B,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAC;CACzD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,kEAAkE,EAAE,YAAY;CACtF,EAAE,IAAI,IAAI,GAAG,GAAE;CACf,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,UAAU,GAAG,GAAE;CAErB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,UAAU;CACb,GAAG,cAAc,CAAC,UAAU,EAAE;CAE9B,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY;CAC5B,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACpB,GAAG,CAAC,CAAC;CACL,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW;CACnC,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;CAC9D,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;CAC9C,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;CACvB,EAAE,IAAI,gBAAgB,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;CAChI,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;CAC1B,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;CAC3D,EAAE,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;CAC9B,GAAG,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;CACxB,GAAG;CACH,EAAE,CAAC,CAAC;AACJ;CACA,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW;CACtC,GAAG,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,aAAa,EAAC;CAC1C,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAC;CAE1B,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CACjC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;CACZ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;CACZ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;CACZ,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAC;CACxB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;CACnD,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAC;CAE1C,KAAK;CACL,IAAI;CACJ,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,8BAA8B,EAAE,WAAW;CACjD,EAAE,MAAM,MAAM,GAAG;CACjB,GAAG,KAAK,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC;CAC3B,GAAG,cAAc,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;CACrE,IAAG;CACH,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;CAC1B,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG,EAAC;AACJ;CACA,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAC;CACvC,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC/C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAC;CAChE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,EAAC;CAC5D,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAC;CAClF,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAC;CAC9F,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAC;CAC1F,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,oCAAoC,EAAE,WAAW;CACvD,EAAE,IAAI,MAAM,GAAG;CACf,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG,QAAQ,EAAE;CACb,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;CACrB,IAAI;CACJ,GAAG,KAAK,EAAE,IAAI,WAAW,CAAC,EAAE,CAAC;CAC7B,IAAG;CACH,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,EAAC;CACvC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAC;CACX,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAC;CACX,EAAE,MAAM,CAAC,IAAI,GAAG,OAAM;CACtB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAM;CAC7B,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAC;CACzC,EAAE,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,SAAQ;CACxC,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG,eAAe,EAAE,IAAI;CACxB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAC;CACrC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,EAAC;CAC/C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,EAAC;CACtD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAC;CACtD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC;CAClE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,aAAa,EAAC;CACjE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,EAAC;CAClE,EAAE,EAAE,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,KAAK,EAAC;CACzC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CACxB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CACxB,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,oDAAoD,EAAE,WAAW;CACvE,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACrB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;AACtE;CACA,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG,eAAe,EAAE,IAAI;CACxB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC;CAClC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAC;CACpD,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAG;CAC9C,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,YAAY,EAAC;AACnD;CACA,EAAE,IAAI,WAAW,GAAG,IAAI,GAAG,GAAE;CAC7B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAC/B,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,EAAE,WAAW,EAAC;CACjD,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAC;CAC3D,GAAG,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAC;CACzC,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,EAAC;CACzC,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,EAAC;CAC9C,GAAG;AACH;CACA,EAAE,IAAI,WAAW,GAAG,IAAI,GAAG,GAAE;CAC7B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAC/B,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,EAAC;CACpC,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAC;CAC3D,GAAG,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAC;CACzC,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,EAAC;CACzC,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,EAAC;CAC9C,GAAG;CACH,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,2BAA2B,EAAE,WAAW;CAC9C,EAAE,IAAI,CAAC,GAAG,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,EAAC;CAC9E,EAAE,IAAI,EAAE,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAC;CAC3C,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,KAAI;CACd,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAC;CACX,EAAE,IAAI,MAAM,GAAG;CACf,GAAG,KAAK,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC;CAC3B,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;CAC3B,GAAG,MAAM,EAAE,QAAQ;CACnB,GAAG,YAAY,EAAE,EAAE;CACnB,GAAG,WAAW,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACvC,GAAG,WAAW,EAAE,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM;CACnD,GAAG,QAAQ,EAAE,IAAI,QAAQ,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;CAC9D,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG,eAAe,EAAE,IAAI;CACxB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAC;CACrC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACxE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAC;CAChE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAC;CACtD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,cAAc,EAAC;CAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAC;CAClD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CAC/C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,EAAC;CACxE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CAC9C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC;CAC9C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,EAAC;CACxE,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,EAAE,MAAM,EAAC;CAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAC;CAClE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAC;CACzD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW;CACvC,EAAE,MAAM,eAAe,GAAG,CAAC,aAAa,EAAE,IAAI,EAAC;CAC/C,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,eAAe,EAAC;CAC3C,EAAE,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAC,eAAe,EAAC;AAC/C;CACA,EAAE,MAAM,OAAO,GAAG;CAClB,GAAG,MAAM,EAAE;CACX,IAAI;CACJ,KAAK,GAAG,EAAE,0BAA0B;CACpC,KAAK;CACL,IAAI;CACJ,IAAG;AACH;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CAClC,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC;CACzC,GAAG;CACH,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;CAC3D,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAClE,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,uCAAuC,EAAE,WAAW;CAC1D,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC;CAC3B,GAAG,eAAe,EAAE,IAAI;CACxB,GAAG,aAAa,EAAE,IAAI;CACtB,GAAG,CAAC,CAAC;AACL;CACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC;AACf;CACA,EAAE,IAAI,MAAM,GAAG;CACf,GAAG,KAAK,EAAE,CAAC;CACX,GAAG,KAAK,EAAE,CAAC;CACX,GAAG,CAAC;AACJ;CACA,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;CACxD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;AACvD;CACA,EAAE,MAAM,GAAG,EAAE,CAAC;CACd,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC;AACvB;CACA,EAAE,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;CACpD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;CAChD,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,0BAA0B,EAAE,UAAU;CAC5C,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAChC,EAAE,IAAI,CAAC,IAAI,GAAG,EAAC;CACf,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAU;CACzC,EAAE,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;CAC5D,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;CACjD,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW;CACvC,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;CAC1B,GAAG,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;CAChD,GAAG,CAAC,CAAC;CACL,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC;CAC3B,GAAG,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;CAChD,GAAG,CAAC,CAAC;CACL,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACjE;CACA,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,wBAAwB,EAAE,WAAW;CAC3C,EAAE,IAAI,IAAI,GAAG,GAAE;CACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAChC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAC;CAC5B,GAAG;CACH,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,gBAAe;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,UAAU;CACb,GAAG,cAAc,CAAC,UAAU,EAAE;CAC9B,IAAI,eAAe,GAAG,WAAU;CAChC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACzC,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,EAAC;CAC1C,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAC;CACnD,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAC;CACtC,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC;CACpB,GAAG,UAAU;CACb,GAAG,mBAAmB,EAAE,GAAG;CAC3B,GAAG,cAAc,CAAC,UAAU,EAAE;CAC9B,IAAI,eAAe,GAAG,WAAU;CAChC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAC;CAC/C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAC;CACtC,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC;CACpB,GAAG,UAAU;CACb,GAAG,mBAAmB,EAAE,GAAG;CAC3B,GAAG,cAAc,CAAC,UAAU,EAAE;CAC9B,IAAI,eAAe,GAAG,WAAU;CAChC,IAAI;CACJ,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,EAAC;CAC3C,EAAE,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CACzC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;AACtC;CACA,EAAE,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAC;CAC/C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,EAAC;AAC3C;CACA,EAAE,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CACzC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,GAAG,EAAC;CAClD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,wBAAwB,EAAE,WAAW;CAC3C,EAAE,MAAM,UAAU,GAAG,GAAE;CACvB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAC/B,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAC;CAC7B,GAAG;CACH,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,EAAC;CACrC,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;CAC1B,GAAG,aAAa,GAAG;CACnB,IAAI,OAAO,UAAU;CACrB,IAAI;CACJ,GAAG,cAAc,CAAC,UAAU,EAAE;CAC9B,IAAI;CACJ,GAAG,mBAAmB,EAAE,GAAG;CAC3B,GAAG,EAAC;CACJ,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC;CAC3B,GAAG,aAAa,GAAG;CACnB,IAAI,OAAO,WAAW;CACtB,IAAI;CACJ,GAAG,cAAc,CAAC,UAAU,EAAE;CAC9B,IAAI;CACJ,GAAG,mBAAmB,EAAE,GAAG;CAC3B,GAAG,EAAC;CACJ,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,EAAE,EAAC;CAC7B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAC;CACtC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAC;CAC1C,EAAE,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,EAAC;CACzC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW;CAC/B,EAAE,IAAI,IAAI,GAAG,UAAS;CACtB,EAAE,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CACzC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAC;CACzB,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAC;CAC/B,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAC;CACrC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAU;CAClC,EAAE,IAAI,IAAI,GAAG,GAAE;CACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;CACjC,GAAG,IAAI,GAAG,GAAG,OAAM;CACnB,GAAG,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,QAAQ,EAAE;CACxD,IAAI,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAG;CACpE,IAAI;CACJ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;CACjB,GAAG;CACH,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU;CAC5B,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACrB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAC;CACpB,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAC;AACrB;AACA;CACA,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,GAAG,EAAE,GAAG;CACX,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC;CAChC,GAAG,aAAa,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC;CACzC,GAAG,iBAAiB,EAAE,IAAI,IAAI,CAAC,0BAA0B,CAAC;CAC1D,GAAG,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC;CACpC,GAAG,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;CACnC,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,GAAE;CACzB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAC;CAC/C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAC;CAChD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,aAAa,EAAC;CAC1D,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,aAAa,EAAC;CACnE,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,0BAA0B,EAAC;CACxF,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,aAAa,EAAC;CAC9D,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,cAAc,EAAC;CACnE,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAU;CACzC,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACrB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAC;CACpB,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAC;AACrB;AACA;CACA,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,GAAG,EAAE,GAAG;CACX,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC;CAChC,GAAG,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;CACnC,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,aAAa,EAAE,IAAI;CACtB,GAAG,cAAc,EAAE,IAAI;CACvB,GAAG,aAAa,EAAE,MAAM,qBAAqB;CAC7C,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAC;CAC3C,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAC;CACzC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,aAAa,EAAC;CAC1D,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,qBAAqB,EAAC;CAC/D,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW;CAChC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,GAAG,EAAE,CAAC;CACT,GAAG,GAAG,EAAE,MAAM;CACd,GAAG,IAAI,EAAE,CAAC;CACV,GAAG,KAAK,EAAE,CAAC;CACX,GAAG,GAAG,EAAE,CAAC;CACT,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC;CACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;CAC5B,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,EAAC;CACjB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC;CACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,IAAI,GAAG,iBAAgB;CACzB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,IAAI,GAAG,kEAAiE;CAC1E,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAW;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,KAAK;CACX,GAAG,CAAC,EAAE,SAAS;CACf,GAAG,CAAC,EAAE,mBAAmB;CACzB,GAAG,CAAC,EAAE,OAAO;CACb,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,UAAU,EAAE,WAAW;CAC1B,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,EAAC;CACrC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,2CAA2C,EAAE,WAAW;CAC9D,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,mBAAmB;CACzB,IAAG;AACH;CACA,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,GAAE;CACzB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,EAAC;CACvD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAW;CACpC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,mBAAmB;CACzB,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,WAAW,EAAE,QAAQ;CACxB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,EAAC;CACtD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,6BAA6B,EAAE,WAAW;CAChD;CACA,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,mBAAmB;CACzB,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,aAAa,EAAE,IAAI;CACtB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,EAAC;CACtD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,kCAAkC,EAAE,WAAW;CACrD,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,CAAC,iBAAiB;CACxB,GAAG,CAAC,EAAE,CAAC,iBAAiB;CACxB,GAAG,CAAC,EAAE,EAAE;CACR,GAAG,CAAC,EAAE,iBAAiB;CACvB,GAAG,CAAC,EAAE,iBAAiB;CACvB,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,WAAW,EAAE,MAAM;CACtB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,iBAAiB,EAAC;CACtD,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,gBAAgB,EAAC;CACrD,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAC;CACrC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,gBAAgB,EAAC;CACpD,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,EAAC;CACrD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,kBAAkB,EAAE,WAAW;CACrC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,CAAC,EAAE,mBAAmB;CACzB,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,WAAW,EAAE,QAAQ;CACxB,GAAG,EAAC;CACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,oBAAoB,EAAC;CACxD,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,2BAA2B,EAAE,UAAU;CAC7C,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;CAC1B,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;CAClC,EAAE,CAAC,CAAC;CACJ,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU;CAC3B,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,YAAY,EAAE,cAAc;CAC/B,GAAG,SAAS,EAAE,aAAa;CAC3B,GAAG,OAAO,EAAE,mBAAmB;CAC/B,GAAG,OAAO,EAAE,QAAQ;CACpB,GAAG,QAAQ,EAAE,CAAC,KAAK;CACnB,GAAG,WAAW,EAAE,SAAS;CACzB,GAAG,IAAI,EAAE,UAAU;CACnB,GAAG,IAAI,EAAE,CAAC;CACV;CACA,GAAG,QAAQ,EAAE,QAAQ;CACrB,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU;CAC1B,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,WAAW,EAAE,IAAI;CACpB,GAAG,mBAAmB,EAAE,CAAC,UAAU;CACnC,GAAG,SAAS,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE;CAC1B,GAAG,iBAAiB,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;CAChC,GAAG,eAAe,EAAE,EAAE;CACtB,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,IAAI,SAAS,GAAG;CAClB,GAAG,MAAM,EAAE,EAAE,EAAE,GAAG;CAClB,IAAG;CACH,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC,SAAS,EAAC,EAAE,EAAC;CAC3D,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;CACxB,GAAG,kBAAkB,EAAE,IAAI;CAC3B,GAAG,EAAC;CACJ,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAC;CACpC,EAAE,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACnC,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE,GAAG,EAAC;AAC9C;CACA,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC;CACpB,GAAG,mBAAmB,EAAE,IAAI;CAC5B,GAAG,EAAC;CACJ,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAC;CACpC,EAAE,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACnC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC;CACzD,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW;CACjC,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,OAAO,EAAC;CACpD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,IAAI,EAAC;CAClD,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU;CAC3B,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,OAAO,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACnC,GAAG,OAAO,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC5C,IAAG;CACH,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW;CAClC,EAAE,MAAM,IAAI,GAAG;CACf,IAAI,GAAG,EAAE,CAAC;CACV,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;CACnC,IAAI,MAAM,EAAE;CACZ,MAAM,GAAG,EAAE,IAAI;CACf,MAAM,GAAG,EAAE,CAAC,UAAU;CACtB,MAAM,MAAM,EAAE;CACd,QAAQ,GAAG,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5C,QAAQ,GAAG,EAAE,GAAG;CAChB,QAAQ,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,2BAA2B,CAAC;CAC1D,OAAO;CACP,KAAK;CACL,GAAG,CAAC;CACJ,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW;CACvC,EAAE,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC;AAClF;CACA,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;CACxB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACxB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACpC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CAC3C,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;CAChE,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW;CAC5C,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;CACrB,EAAE,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC,kOAAkO,EAAC;CAChR,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACvC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAC;CACtC,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM;CAC9B,EAAE,IAAI,MAAM,GAAG,cAAc,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;CAC3D,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;CACxC,EAAE,MAAM,GAAG,GAAE;CACb,EAAE,cAAc,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAC;CAC3E,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;CACxC,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,+BAA+B,EAAE,MAAM;CAC7C,EAAE,IAAI,MAAM,GAAG,cAAc,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;CAC3D,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;CACxC,EAAE,MAAM,GAAG,GAAE;CACb,EAAE,cAAc,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAC;CACnG,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;CAChE,EAAE,EAAC;AACH;CACA,CAAC,IAAI,CAAC,4BAA4B,EAAE,MAAM;CAC1C,EAAE,MAAM,YAAY,CAAC;CACrB,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC1B,GAAG,MAAM,GAAG;CACZ,IAAI,OAAO,IAAI;CACf,IAAI;CACJ,GAAG;CACH,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,YAAY,EAAC;CAC3C,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,EAAC;CACzC,EAAE,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAC;CAClE,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY;CACjC,EAAE,IAAI,IAAI,GAAG;CACb,GAAG,IAAI,EAAE;CACT,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACnD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;CACzD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACrD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;CACpD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACpD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;CACpD,IAAI;CACJ,GAAG,WAAW,EAAE,YAAY;CAC5B,GAAG,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;CACzB,GAAG,gBAAgB,EAAE;CACrB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;CACf,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;CACf,IAAI,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;CAC/B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;CAClB,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,QAAQ,GAAG;CACjB,GAAG,IAAI,EAAE;CACT,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACnD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;CACvC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACrD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG;CACzC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;CACpD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;CACvC,IAAI;CACJ,GAAG,WAAW,EAAE,YAAY;CAC5B,GAAG,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;CACzB,GAAG,gBAAgB,EAAE;CACrB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;CACpB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;CACf,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;CACf,IAAI,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;CAC/B,IAAI,EAAE;CACN,IAAI;CACJ,IAAG;CACH,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAC;CAC7E,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC7C,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,EAAC;CAC1C,EAAE,EAAC;CACH,CAAC,EAAC;CACF,KAAK,CAAC,4BAA4B,EAAE,UAAU;CAC9C,CAAC,IAAI,CAAC,wBAAwB,EAAE,WAAW;CAC3C,EAAE,IAAI,IAAI,GAAG,WAAU;CACvB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;CAErB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;CACvC,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,EAAC;CAC7C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;CACvC,GAAsB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAC;CAC5C,GAAG;CACH,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,4BAA4B,EAAE,WAAW;CAC/C,EAAE,IAAI,IAAI,GAAG,WAAU;CACvB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;CACrB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;CACvC,GAAoB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;CACxC,GAAG;CACH,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW;CACvC,EAAE,IAAI,IAAI,GAAG,WAAU;CACvB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;CACrB,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,EAAC;CAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,UAAU,CAAC,MAAM,EAAC;CACpD,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK,EAAE,EAAC;CAC7D,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnC,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,UAAU,CAAC,MAAM,EAAC;CAC/D,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;CACvC,GAAsB,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC;CAC9C,GAAG;CACH,EAAE,EAAC;CACH,CAAC,IAAI,CAAC,kBAAkB,EAAE,WAAW;CACrC,EAAE,IAAI,IAAI,GAAG,WAAU;CACvB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;CACrB,EAAE,IAAI,UAAU,GAAG,GAAE;CACrB,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK,EAAE,EAAC;CAC7D,EAAE,IAAI,MAAM,GAAG,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,UAAU,CAAC,OAAO,EAAC;AAC7F;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;CACvC;CACA,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAC;CAC1B,GAAoB,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACpC;CACA,GAAG;CACH;CACA,EAAE,EAAC;CACH,CAAC;;;;;;"}