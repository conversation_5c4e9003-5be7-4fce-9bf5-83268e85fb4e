const express = require("express");
const { default: mongoose } = require("mongoose");
const { runXtranferTest } = require("./process/xtransfer");
const Joi = require("joi");
const { rangeOfRandomPhNumbers } = require("./utils");

const app = express();
app.use(express.json());

const xTransferSchema = Joi.object({
  start: Joi.number().required(),
  end: Joi.number().required(),
  country: Joi.string().required(),
  proxyCountry: Joi.string().required(),
});

app.post("/xtransfer", (req, res) => {
  const { error } = xTransferSchema.validate(req.body);
  if (error) return res.status(400).send(error.details[0].message);

  const { start, end, country, proxyCountry } = req.body;

  rangeOfRandomPhNumbers(start, end).forEach((phNumber) => {
    runXtranferTest({ country, phNumber, proxyCountry });
  });

  res.send("OK");
});

/************** */
app.listen(3000, () => {
  console.log("Server started on port 3000");
});

mongoose
  .connect(
    "mongodb+srv://rafeeq:<EMAIL>/automate-tester"
  )
  .then(() => console.log("connected to database"));
