const fs = require("fs").promises;
const fsSync = require("fs");
const path = require("path");

async function saveBase64Image({ base64Data, filename }) {
  try {
    // Validate input parameters
    if (!base64Data || !filename) {
      throw new Error("Missing required parameters: base64Data or filename");
    }

    // Remove data URL prefix if present
    const matches = base64Data.match(/^data:(.+);base64,(.+)$/);
    const base64String = matches ? matches[2] : base64Data;

    // Validate base64 string
    if (!base64String) {
      throw new Error("Invalid base64 data provided");
    }

    const buffer = Buffer.from(base64String, "base64");
    const publicDir = path.join(__dirname, "../public");

    // Create directory if it doesn't exist (using sync for simplicity)
    if (!fsSync.existsSync(publicDir)) {
      fsSync.mkdirSync(publicDir, { recursive: true });
    }

    const filePath = path.join(publicDir, `${filename}.png`);
    await fs.writeFile(filePath, buffer);

    console.log(`Image saved successfully: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error("Failed to save image:", error.message);
    throw error; // Re-throw to allow caller to handle
  }
}

module.exports = {
  saveBase64Image,
};
