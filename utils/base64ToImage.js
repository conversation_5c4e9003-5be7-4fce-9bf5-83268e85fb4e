const fs = require("fs");
const path = require("path");

function saveBase64Image({ base64Data, filename }) {
  // Remove data URL prefix if present
  const matches = base64Data.match(/^data:(.+);base64,(.+)$/);
  const buffer = Buffer.from(matches ? matches[2] : base64Data, "base64");

  const publicDir = path.join(__dirname, "../public");

  // Create directory if it doesn't exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  fs.writeFile(
    path.join(publicDir, `${filename}.png`),
    buffer,
    (err) => err && console.error("Failed to save image:", err)
  );
}

module.exports = {
  saveBase64Image,
};
