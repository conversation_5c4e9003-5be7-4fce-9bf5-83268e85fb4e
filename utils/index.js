function rangeOfRandomPhNumbers(start, end) {
  const startNum = BigInt(start);
  const endNum = BigInt(end);

  if (endNum <= startNum) {
    throw new Error("End number must be greater than start number");
  }

  const range = [];
  for (let i = startNum; i < endNum; i++) {
    range.push(i.toString());
  }

  // Shuffle using Fisher-Yates algorithm
  for (let i = range.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [range[i], range[j]] = [range[j], range[i]];
  }

  return range;
}

module.exports = {
  rangeOfRandomPhNumbers,
};
